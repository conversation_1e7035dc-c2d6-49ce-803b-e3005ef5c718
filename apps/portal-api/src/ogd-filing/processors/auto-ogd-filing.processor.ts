import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Logger, NotFoundException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { CustomsCountry, FIND_PRODUCT_RELATIONS, Product } from "nest-modules";
import { DataSource } from "typeorm";
import { AutoOgdFilingService } from "../auto-ogd-filing.service";
import { AutoOgdFilingJob, DEFAULT_WORKER_OPTIONS, OgdFilingQueueName } from "../types";

@Processor(
  {
    name: OgdFilingQueueName.AUTO_OGD_FILING
  },
  DEFAULT_WORKER_OPTIONS
)
export class AutoOgdFilingProcessor extends WorkerHost {
  constructor(
    @Inject(AutoOgdFilingService)
    private readonly autoOgdFilingService: AutoOgdFilingService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    super();
  }
  private readonly logger = new Logger(AutoOgdFilingProcessor.name);

  async process(job: AutoOgdFilingJob) {
    this.logger.log(`Processing auto OGD filing job. ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { sourceEventType, productId } = job.data;

    const product = await this.dataSource.manager.findOne(Product, {
      where: { id: productId },
      relations: FIND_PRODUCT_RELATIONS
    });

    if (!product) throw new NotFoundException(`Product with ID ${productId} not found`);
    if (product.customsCountry !== CustomsCountry.CANADA) {
      this.logger.log(`Product ${product.id}'s customs country is not Canada, skipping auto OGD filing...`);
      return;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (sourceEventType === "update") {
        job.log(`Job is from product update event, removing all existing OGD filings...`);
        await this.autoOgdFilingService.removeGeneratedOgdFilings(product, queryRunner);
      }

      await this.autoOgdFilingService.autoFillOgdFiling(product, queryRunner);

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error processing auto OGD filing job. ID: ${job.id}, Data: ${JSON.stringify(job.data)}, Error message: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
