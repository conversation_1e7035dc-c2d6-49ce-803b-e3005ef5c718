import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger, Type } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import {
  CanadaOgd,
  CustomsCountry,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  OgdFiling,
  Product,
  RuleQueryService,
  UserPermission
} from "nest-modules";
import { QueryRunner } from "typeorm";
import { ProductCreatedEventPayload, ProductUpdatedEventPayload } from "../product/events";
import { OgdFilingService } from "./ogd-filing.service";
import { OgdProgramCollection } from "./strategies/base.strategy";
import { EcccStrategy } from "./strategies/eccc.strategy";
import { GacStrategy } from "./strategies/gac.strategy";
import { HealthCanadaStrategy } from "./strategies/health-canada.strategy";
import { NrcanStrategy } from "./strategies/nrcan.strategy";
import { ApplicableOgdFiling, AutoOgdFilingQueue, OgdFilingQueueName } from "./types";

@Injectable()
export class AutoOgdFilingService {
  constructor(
    @InjectQueue(OgdFilingQueueName.AUTO_OGD_FILING)
    private readonly autoOgdFilingQueue: AutoOgdFilingQueue,
    private readonly moduleRef: ModuleRef,
    private readonly healthCanadaStrategy: HealthCanadaStrategy,
    private readonly gacStrategy: GacStrategy,
    private readonly nrcanStrategy: NrcanStrategy,
    private readonly ecccStrategy: EcccStrategy
  ) {}

  private readonly logger = new Logger(AutoOgdFilingService.name);

  private async getRuleQueryService(organizationId: number): Promise<RuleQueryService> {
    return this.getService(organizationId, RuleQueryService);
  }

  private async getMatchingRuleService(organizationId: number): Promise<MatchingRuleService> {
    return this.getService(organizationId, MatchingRuleService);
  }

  private async getOgdFilingService(organizationId: number): Promise<OgdFilingService> {
    return this.getService(organizationId, OgdFilingService);
  }

  private async getService(organizationId: number, service: Type<any>) {
    // set org id
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.ORGANIZATION_ADMIN,
          organization: {
            id: organizationId
          }
        }
      },
      contextId
    );
    const resolved = await this.moduleRef.resolve(service, contextId, {
      strict: false
    });

    // @see: https://github.com/nestjs/nest/issues/5778
    await new Promise((resolve) => process.nextTick(resolve));

    this.logger.debug("Current org id: " + organizationId);
    return resolved;
  }

  async createProductOgdFiling(product: ProductCreatedEventPayload) {
    // // auto fill the ogd filing
    // await this.autoFillOgdFiling(product);
    await this.autoOgdFilingQueue.add(
      product.id.toString(),
      {
        sourceEventType: "create",
        productId: product.id
      },
      {
        deduplication: {
          id: product.id.toString(),
          ttl: 5000 // 5 seconds
        }
      }
    );
  }

  async updateProductOgdFiling(product: ProductUpdatedEventPayload) {
    // // clear cache
    // // remove all existing generated matching rules
    // await this.removeGeneratedOgdFilings(product);
    // // auto fill the ogd filing
    // await this.autoFillOgdFiling(product);
    await this.autoOgdFilingQueue.add(
      product.id.toString(),
      {
        sourceEventType: "update",
        productId: product.id
      },
      {
        deduplication: {
          id: product.id.toString(),
          ttl: 5000 // 5 seconds
        }
      }
    );
  }

  /**
   * Get all ongoing auto OGD filing jobs, which includes all running and waiting jobs.
   * @param productIds IDs of products to filter the jobs
   * @returns List of ID, status and data of ongoing auto OGD filing jobs
   */
  async getOngoingAutoOgdFilingJobs(productIds?: Array<number>) {
    const jobs = await this.autoOgdFilingQueue.getJobs(
      ["waiting-children", "waiting", "delayed", "active", "prioritized", "paused", "repeat", "wait"],
      0
    );
    return await Promise.all(
      jobs
        .filter((job) => (Array.isArray(productIds) ? productIds.includes(job.data.productId) : true))
        .map(async (job) => ({
          jobId: job.id,
          status: await job.getState(),
          data: job.data
        }))
    );
  }

  async getStrategy(agency: string) {
    switch (agency) {
      case "hc":
        return this.healthCanadaStrategy;
      case "gac":
        return this.gacStrategy;
      case "nrcan":
        return this.nrcanStrategy;
      case "eccc":
        return this.ecccStrategy;
    }
  }

  async autoFillOgdFiling(product: Product, queryRunner?: QueryRunner) {
    if (product.customsCountry !== CustomsCountry.CANADA) {
      this.logger.log(`Product ${product.id}'s customs country is not Canada, skipping auto OGD filing...`);
      return;
    }

    this.logger.log("Auto filing OGD filing for product: " + product.id);
    const ogdPrograms = await this.getApplicableOgdProgramsForProduct(product, queryRunner);

    for (const [agency, possibleApplicablePrograms] of Object.entries(ogdPrograms)) {
      this.logger.log("Agency: " + agency);

      if (!possibleApplicablePrograms.length) {
        this.logger.log("No applicable programs found for agency: " + agency);
        continue;
      }

      this.logger.log(
        "Possible applicable programs: " +
          possibleApplicablePrograms.map((program) => program.program).join(", ")
      );

      let applicableFilings: ApplicableOgdFiling[] = [];

      const programCollection = new OgdProgramCollection(possibleApplicablePrograms);

      const strategy = await this.getStrategy(agency);

      if (!strategy) {
        this.logger.error("Strategy not found for agency: " + agency);
        continue;
      }

      applicableFilings.push(...(await strategy.getApplicableFilings(product, programCollection)));

      for (const filing of applicableFilings) {
        const service = await this.getOgdFilingService(product.organization.id);
        const matchingRuleService = await this.getMatchingRuleService(product.organization.id);

        const suffix = new Date().getTime().toString(36);

        const matchingRule = {
          name: `CanadaOgdFiling - ${agency} - ${filing.program} - product id equals ${product.id} - ${suffix}`,
          destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT
        };

        this.logger.log("Matching rule: " + JSON.stringify(matchingRule));

        const response = await service.createOgdFilingAndMatchingRule(
          {
            matchingRule,
            ogdFiling: filing.payload,
            matchingConditions: [
              {
                attribute: "id",
                isOperationInverted: false,
                operator: MatchingConditionOperator.EQUALS,
                value: product.id.toString()
              }
            ]
          },
          queryRunner
        );

        const matchingRuleId = response.matchingRule.id;

        // activate the matching rule
        await matchingRuleService.updateMatchingRuleStatus(
          matchingRuleId,
          {
            status: MatchingRuleStatus.ACTIVE
          },
          queryRunner
        );
      }
    }
  }

  async removeGeneratedOgdFilings(product: Product, queryRunner?: QueryRunner) {
    const matchingRuleService = await this.getMatchingRuleService(product.organization.id);

    // Get all OGD filing matching rules for the product
    const matchingRules = await matchingRuleService.getMatchingRules(
      {
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        destinationId: product.id,
        sourceTable: MatchingRuleSourceDatabaseTable.OGD_FILING
      },
      queryRunner
    );

    // Filter only the matching rules that are auto created
    const autoCreatedMatchingRules = matchingRules.matchingRules.filter((rule) =>
      rule.conditions.every(
        (condition) => condition.attribute === "id" || condition.attribute === "organizationId"
      )
    );

    // Disable and delete all auto created matching rules
    for (const matchingRule of autoCreatedMatchingRules) {
      await matchingRuleService.updateMatchingRuleStatus(
        matchingRule.id,
        {
          status: MatchingRuleStatus.DISABLED
        },
        queryRunner
      );
      await matchingRuleService.deleteMatchingRule(matchingRule.id, queryRunner);
    }
    this.logger.log("Removed " + autoCreatedMatchingRules.length + " auto created matching rules");

    // TODO: Delete all auto created OGD filings
  }

  async getApplicableOgdProgramsForProduct(
    product: Product,
    queryRunner?: QueryRunner
  ): Promise<Record<string, { id: number; program: string }[]>> {
    const ruleQueryService = await this.getRuleQueryService(product.organization.id);

    const queryResult = await ruleQueryService.queryMatchingResults(
      {
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        destinationId: product.id,
        sourceTables: [MatchingRuleSourceDatabaseTable.CANADA_OGD, MatchingRuleSourceDatabaseTable.OGD_FILING]
      },
      queryRunner
    );

    const canadaOgdMatchingResults = queryResult.matchingResults.find(
      (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
    );

    const existingOgdFilings = queryResult.matchingResults.find(
      (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING
    );

    if (!canadaOgdMatchingResults) {
      return {};
    }

    const ogdPrograms = canadaOgdMatchingResults.sourceRecords
      .map((result: CanadaOgd) => ({
        id: result.id,
        agency: result.agency,
        program: result.program,
        commodityType: result.commodityType
      }))
      .filter(
        // we will not auto fill the ogd filing if it already exists
        (ogdProgram) =>
          !existingOgdFilings.sourceRecords.some(
            (filing: OgdFiling) => filing.ogd.agency === ogdProgram.agency
          )
      );

    // group by agency, and ogd id
    const groupedOgdPrograms = ogdPrograms.reduce((acc, curr) => {
      acc[curr.agency] = acc[curr.agency] || [];
      acc[curr.agency].push({
        id: curr.id,
        program: curr.program,
        commodityType: curr.commodityType
      });
      return acc;
    }, {});

    return groupedOgdPrograms;
  }
}
