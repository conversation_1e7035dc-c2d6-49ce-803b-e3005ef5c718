import { CommercialInvoiceLineSyncService } from "@/commercial-invoice/commercial-invoice-line-sync.service";
import { CommercialInvoiceLineService } from "@/commercial-invoice/commercial-invoice-line.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  BatchUpdateProductsDto,
  BatchUpdateProductsResponseDto,
  CanadaTariffService,
  CandataService,
  CommercialInvoice,
  CommercialInvoiceLine,
  convertFromCamelCase,
  CountryService,
  CreateProductDto,
  CustomsCountry,
  EditProductDto,
  FIND_PRODUCT_RELATIONS,
  getFindOptions,
  GetProductsDto,
  GetProductsResponseDto,
  GetRelatedCommercialInvoiceLinesDto,
  GetRelatedCommercialInvoiceLinesResponseDto,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleService,
  PartnerType,
  Product,
  PRODUCT_CASE_INSENSITIVE_KEYS,
  PRODUCT_ENUM_KEYS,
  PRODUCT_REQUIRED_KEYS,
  ProductColumn,
  ProductType,
  RelatedCommercialInvoiceLineDto,
  StateService,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { DataSource, In, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { TradePartnerService } from "../trade-partner/trade-partner.service";
import { ProductEvent } from "./events";

@Injectable({ scope: Scope.REQUEST })
export class ProductService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(StateService)
    private readonly stateService: StateService,
    @Inject(forwardRef(() => CommercialInvoiceLineService))
    private readonly commercialInvoiceLineService: CommercialInvoiceLineService,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(CanadaTariffService)
    private readonly canadaTariffService: CanadaTariffService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    @Inject(TransactionalEventEmitterService)
    private readonly transactionalEventEmitter: TransactionalEventEmitterService,
    @Inject(forwardRef(() => CommercialInvoiceLineSyncService))
    private readonly commercialInvoiceLineSyncService: CommercialInvoiceLineSyncService
  ) {}

  private readonly logger = new Logger(ProductService.name);

  private async createAndValidateProduct(
    createProductDto: CreateProductDto,
    queryRunner?: QueryRunner,
    errorMsgPrefix = ""
  ) {
    const { vendorId, originId, manufacturerId, originStateId, ...createProductProps } = createProductDto;

    const newProduct = new Product();
    newProduct.organization = this.request?.user?.organization;
    newProduct.vendor = await this.tradePartnerService.getTradePartnerById(vendorId, queryRunner);
    newProduct.origin = await this.countryService.getCountryById(originId, queryRunner);

    // Origin State
    if (newProduct.origin.alpha2 === "US") {
      if (typeof originStateId === "number") {
        newProduct.originState = await this.stateService.getStateById(originStateId);
        if (!newProduct.originState) throw new NotFoundException(`${errorMsgPrefix}Origin state not found`);
      }
    } else {
      newProduct.originState = null;
    }

    if (typeof manufacturerId === "number") {
      newProduct.manufacturer = await this.tradePartnerService.getTradePartnerById(
        manufacturerId,
        queryRunner
      );
      if (!newProduct.manufacturer) throw new NotFoundException(`${errorMsgPrefix}Manufacturer not found`);
      if (newProduct.manufacturer.organization?.id !== this.request?.user?.organization?.id)
        throw new BadRequestException(
          `${errorMsgPrefix}Manufacturer does not belong to the organization of current user`
        );
    }
    newProduct.createdBy = this.request?.user || null;
    newProduct.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createProductProps)) {
      if (value === undefined) continue;
      if (PRODUCT_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
      newProduct[key] =
        value !== null ? (PRODUCT_CASE_INSENSITIVE_KEYS.includes(key) ? value.toUpperCase() : value) : null;
    }
    if (!newProduct.productType) newProduct.productType = ProductType.REGULAR;
    if (!newProduct.customsCountry) newProduct.customsCountry = CustomsCountry.CANADA;

    if (!newProduct.vendor) throw new NotFoundException(`${errorMsgPrefix}Vendor not found`);
    if (newProduct.vendor.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException(
        `${errorMsgPrefix}Vendor does not belong to the organization of current user`
      );
    if (![PartnerType.VENDOR, PartnerType.SHIPPER].includes(newProduct.vendor.partnerType))
      throw new BadRequestException(`${errorMsgPrefix}Vendor is not in type vendor or shipper`);
    if (!newProduct.origin) throw new NotFoundException(`${errorMsgPrefix}Origin not found`);
    // TODO: Check HS code based on customs country
    if (!(await this.canadaTariffService.getCanadaTariffByHsCode(newProduct.hsCode, true, queryRunner)))
      throw new BadRequestException(`${errorMsgPrefix}HS code is invalid under current Canada tariff`);
    return newProduct;
  }

  private async editAndValidateProduct(
    productId: number,
    editProductDto: EditProductDto,
    queryRunner?: QueryRunner,
    errorMsgPrefix = ""
  ) {
    const { vendorId, originId, manufacturerId, originStateId, ...editProductProps } = editProductDto;
    const product = await this.getProductById(productId, queryRunner);
    if (!product) throw new NotFoundException(`${errorMsgPrefix}Product not found`);
    if (product.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException(
        `${errorMsgPrefix}Product does not belong to the organization of current user`
      );

    product.lastEditedBy = this.request?.user || null;
    if (typeof vendorId === "number") {
      product.vendor = await this.tradePartnerService.getTradePartnerById(vendorId, queryRunner);
      if (!product.vendor) throw new NotFoundException(`${errorMsgPrefix}Vendor not found`);
      if (product.vendor.organization?.id !== this.request?.user?.organization?.id)
        throw new BadRequestException(
          `${errorMsgPrefix}Vendor does not belong to the organization of current user`
        );
      if (![PartnerType.VENDOR, PartnerType.SHIPPER].includes(product.vendor.partnerType))
        throw new BadRequestException(`${errorMsgPrefix}Vendor is not in type vendor or shipper`);
    }
    if (typeof originId === "number") {
      product.origin = await this.countryService.getCountryById(originId, queryRunner);
      if (!product.origin) throw new NotFoundException(`${errorMsgPrefix}Origin not found`);

      // Origin State
      if (product.origin.alpha2 === "US") {
        if (typeof originStateId === "number") {
          product.originState = await this.stateService.getStateById(originStateId);
          if (!product.originState) throw new NotFoundException(`${errorMsgPrefix}Origin state not found`);
        }
      } else {
        product.originState = null;
      }
    }
    if (typeof manufacturerId === "number") {
      product.manufacturer = await this.tradePartnerService.getTradePartnerById(manufacturerId, queryRunner);
      if (!product.manufacturer) throw new NotFoundException(`${errorMsgPrefix}Manufacturer not found`);
      if (product.manufacturer.organization?.id !== this.request?.user?.organization?.id)
        throw new BadRequestException(
          `${errorMsgPrefix}Manufacturer does not belong to the organization of current user`
        );
    } else if (manufacturerId === null) product.manufacturer = null;
    for (const [key, value] of Object.entries(editProductProps)) {
      if (value === undefined) continue;
      if (PRODUCT_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
      product[key] =
        value !== null ? (PRODUCT_CASE_INSENSITIVE_KEYS.includes(key) ? value.toUpperCase() : value) : null;
    }
    if (!product.customsCountry) product.customsCountry = CustomsCountry.CANADA;
    // TODO: Check HS code based on customs country
    if (
      product.hsCode &&
      !(await this.canadaTariffService.getCanadaTariffByHsCode(product.hsCode, true, queryRunner))
    )
      throw new BadRequestException(`${errorMsgPrefix}HS code is invalid under current Canada tariff`);
    return product;
  }

  async getProducts(getProductsDto: GetProductsDto): Promise<GetProductsResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getProductsDto.organizationId = this.request?.user?.organization?.id || -1;
    const { where, order, skip, take } = getFindOptions(
      getProductsDto,
      PRODUCT_ENUM_KEYS,
      [],
      ProductColumn.id
    );
    const [products, total] = await this.productRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: FIND_PRODUCT_RELATIONS
    });
    return {
      products,
      total,
      skip,
      limit: take
    };
  }

  async getProductById(productId: number, queryRunner?: QueryRunner) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;
    return await productRepository.findOne({
      where: {
        id: productId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_PRODUCT_RELATIONS
    });
  }

  /**
   * Check if product exists.
   *
   * @param partNumber - Part number
   * @param vendorId - Vendor ID
   * @param originId - Origin ID
   * @param sku - SKU
   * @param upc - UPC
   * @param queryRunner - Query runner
   * @returns True if product exists, false otherwise
   */
  async isProductExists(
    customsCountry: CustomsCountry,
    partNumber: string,
    vendorId: number,
    originId: number,
    sku?: string | null,
    upc?: string | null,
    productId?: number | null,
    organizationId?: number | null,
    queryRunner?: QueryRunner
  ) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    return await productRepository.existsBy({
      id: Not(typeof productId === "number" ? productId : IsNull()),
      customsCountry,
      partNumber,
      sku: sku || IsNull(),
      upc: upc || IsNull(),
      vendor: { id: vendorId },
      origin: { id: originId },
      organization: { id: organizationId || -1 }
    });
  }

  /**
   * Get product by criteria
   *
   * @deprecated Use ProductMatcher instead
   * @param createProductDto
   * @param queryRunner
   * @returns
   */
  async getProductByCriteria(createProductDto: CreateProductDto, queryRunner?: QueryRunner) {
    if (!createProductDto.customsCountry) createProductDto.customsCountry = CustomsCountry.CANADA;
    const { customsCountry, partNumber, vendorId, originId, sku, upc, hsCode } = createProductDto;

    // at least one of partNumber, sku, upc, hsCode is required
    if (!partNumber && !sku && !upc && !vendorId && !originId && !hsCode) {
      return null;
    }

    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    return await productRepository.findOne({
      where: {
        customsCountry,
        partNumber,
        sku: sku || IsNull(),
        upc: upc || IsNull(),
        vendor: { id: vendorId },
        origin: { id: originId },
        hsCode,
        organization: { id: this.request?.user?.organization?.id || -1 }
      }
    });
  }

  /**
   * Check and convert temporary product to regular product
   *
   * If the product is temporary and is referenced by multiple shipments, it will be converted to regular product.
   *
   * @param productId - Product ID
   * @param queryRunner - Query runner
   */
  async checkAndConvertTemporaryToRegularProduct(productId: number, queryRunner?: QueryRunner) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    this.logger.debug(`Checking if temporary product ${productId} is referenced by multiple shipments...`);

    const result = await productRepository
      .createQueryBuilder("p", queryRunner)
      .select("p.id", "productId")
      .addSelect("p.productType", "productType")
      .addSelect(`COUNT(DISTINCT "ci"."shipmentId")`, "count")
      .leftJoin(CommercialInvoiceLine, "cil", "cil.productId = p.id")
      .leftJoin(CommercialInvoice, "ci", "ci.id = cil.commercialInvoiceId")
      .where("p.id = :productId", { productId })
      .groupBy(`p.id, p."productType"`)
      .getRawOne<{ productId: number; productType: ProductType; count: number | string }>();

    if (!result?.productId || !result?.productType) {
      this.logger.log(
        `Product ${productId} not found, skip checking and converting temporary product to regular product`
      );
      return;
    }

    // @see: https://github.com/typeorm/typeorm/issues/6196
    const referenceShipmentCount = typeof result.count === "number" ? result.count : parseInt(result.count);
    this.logger.debug(
      `Product ${productId} is ${result.productType} and referenced by ${referenceShipmentCount} shipments`
    );

    if (result.productType === ProductType.TEMPORARY && referenceShipmentCount > 1) {
      await productRepository.update(productId, { productType: ProductType.REGULAR });
      this.logger.log(`Converted temporary product ${productId} to regular product`);
    }
  }

  /**
   * Get the number of shipments referencing a product
   *
   * @param productId - Product ID
   * @param queryRunner - Query runner
   * @returns Number of shipments referencing the product
   */
  private async getProductReferenceShipmentCount(
    productId: number,
    queryRunner?: QueryRunner
  ): Promise<number> {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    const result = await productRepository
      .createQueryBuilder("p", queryRunner)
      .select("p.id", "productId")
      .addSelect(`COUNT(DISTINCT "ci"."shipmentId")`, "count")
      .leftJoin(CommercialInvoiceLine, "cil", "cil.productId = p.id")
      .leftJoin(CommercialInvoice, "ci", "ci.id = cil.commercialInvoiceId")
      .where("p.id = :productId", { productId })
      .groupBy(`p.id`)
      .getRawOne<{ productId: number; count: number | string }>();

    return typeof result.count === "number" ? result.count : parseInt(result.count);
  }

  /**
   * Check and remove unreferenced temporary product
   *
   * If the temporary product is not referenced by any shipment, it will be deleted.
   *
   * @param productId - Product ID
   * @param queryRunner - Query runner
   */
  async checkAndRemoveUnreferencedTemporaryProduct(productId: number, queryRunner?: QueryRunner) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    const result = await productRepository
      .createQueryBuilder("p", queryRunner)
      .select("p.id", "productId")
      .addSelect("p.productType", "productType")
      .addSelect(`COUNT(DISTINCT "ci"."shipmentId")`, "count")
      .leftJoin(CommercialInvoiceLine, "cil", "cil.productId = p.id")
      .leftJoin(CommercialInvoice, "ci", "ci.id = cil.commercialInvoiceId")
      .where("p.id = :productId", { productId })
      .groupBy(`p.id, p."productType"`)
      .getRawOne<{ productId: number; productType: ProductType; count: number | string }>();

    if (!result?.productId || !result?.productType) {
      this.logger.log(
        `Product ${productId} not found, skip checking and removing unreferenced temporary product`
      );
      return;
    }

    // @see: https://github.com/typeorm/typeorm/issues/6196
    const referenceShipmentCount = typeof result.count === "number" ? result.count : parseInt(result.count);
    this.logger.debug(
      `Product ${productId} is ${result.productType} and referenced by ${referenceShipmentCount} shipments`
    );

    if (result.productType === ProductType.TEMPORARY && referenceShipmentCount === 0) {
      await productRepository.delete(productId);
      this.logger.log(`Deleted unreferenced temporary product ${productId}`);
    }
  }

  /**
   * Check and remove a list of unreferenced temporary products
   *
   * @param productIds - Product IDs
   * @param queryRunner - Query runner
   */
  async checkAndRemoveUnreferencedTemporaryProducts(productIds: number[], queryRunner?: QueryRunner) {
    for (const productId of productIds) {
      await this.checkAndRemoveUnreferencedTemporaryProduct(productId, queryRunner);
    }
  }

  /**
   * Update or remove temporary products according to the number of shipments referencing them.
   * - If no shipments or CI lines reference the temporary product, it will be removed.
   * - If all CI lines referencing the temporary product is from the same shipment, it will remain temporary.
   * - If CI lines from more than one shipment referencing the temporary product, it will be converted to regular product.
   *
   * This method will be used in batch processing, please do not call this method in other places as it will be slow.
   * @param queryRunner - Query runner that manages the transaction
   */
  @OnEvent(ProductEvent.PRODUCT_CLEAN_UP)
  async updateOrRemoveTemporaryProducts(queryRunner?: QueryRunner) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    // Remove no-reference temporary products
    const noReferenceTempProducts = await productRepository
      .createQueryBuilder("p", queryRunner)
      .leftJoin(
        (qb) =>
          qb
            .select(`cil."productId"`, "productId")
            .addSelect(`COUNT(*)`, "count")
            .from(CommercialInvoiceLine, "cil")
            .groupBy(`cil."productId"`),
        "r",
        `p.id = r."productId"`
      )
      .where(`p."productType" = :productType`, { productType: ProductType.TEMPORARY })
      .andWhere(`(r."count" <= 0 OR r."count" IS NULL)`)
      .getMany();
    this.logger.log(`Found ${noReferenceTempProducts.length} no-reference temporary products, deleting...`);
    this.logger.debug(
      `No-reference temporary products: ${JSON.stringify(noReferenceTempProducts.map((p) => p.id))}`
    );
    if (noReferenceTempProducts.length > 0) {
      const deleteResult = await productRepository.delete({
        id: In(noReferenceTempProducts.map((p) => p.id))
      });
      this.logger.log(`Deleted ${deleteResult.affected} no-reference temporary products`);
    }

    // Update temporary products that are referenced by multiple shipments to regular products
    const multiShipmentTempProducts = await (queryRunner ? queryRunner.manager : this.dataSource)
      .createQueryBuilder()
      .select(`cil."productId"`, "productId")
      .addSelect(`count(*)`, "count")
      .from(
        (qb) =>
          qb
            .select(`p.id`, "productId")
            .addSelect(`ci."shipmentId"`, "shipmentId")
            .from(CommercialInvoiceLine, "cil")
            .leftJoin(CommercialInvoice, "ci", `ci.id = cil."commercialInvoiceId"`)
            .leftJoin(Product, "p", `p.id = cil."productId"`)
            .where(`p."productType" = :productType`, { productType: ProductType.TEMPORARY })
            .distinctOn([`p.id`, `ci."shipmentId"`]),
        "cil"
      )
      .groupBy(`cil."productId"`)
      .having(`count(*) > 1`)
      .getRawMany<{ productId: number; count: number }>();
    this.logger.log(
      `Found ${multiShipmentTempProducts.length} temporary products referenced by multiple shipments, converting to regular products...`
    );
    this.logger.debug(
      `Multi-shipment temporary products: ${JSON.stringify(multiShipmentTempProducts.map((p) => p.productId))}`
    );
    if (multiShipmentTempProducts.length > 0) {
      const updateResult = await productRepository.update(
        { id: In(multiShipmentTempProducts.map((p) => p.productId)) },
        { productType: ProductType.REGULAR }
      );
      this.logger.log(`Converted ${updateResult.affected} temporary products to regular products`);
    }
  }

  /**
   * Create product
   * @param createProductDto - Create product DTO
   * @param queryRunner - Query runner
   * @param temporary - Whether the product is temporary. If true, product will be temporary.
   * @returns Created product
   */
  async createProduct(createProductDto: CreateProductDto, queryRunner?: QueryRunner, temporary = false) {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    if (!createProductDto.customsCountry) createProductDto.customsCountry = CustomsCountry.CANADA;

    if (
      await this.isProductExists(
        createProductDto.customsCountry,
        createProductDto.partNumber,
        createProductDto.vendorId,
        createProductDto.originId,
        createProductDto.sku,
        createProductDto.upc,
        null,
        this.request?.user?.organization?.id,
        queryRunner
      )
    )
      throw new BadRequestException("Product already exists");

    let newProduct = await this.createAndValidateProduct(createProductDto, queryRunner);
    newProduct.productType = temporary ? ProductType.TEMPORARY : ProductType.REGULAR;

    newProduct = await productRepository.save(newProduct);

    this.transactionalEventEmitter.enqueueEvent(ProductEvent.PRODUCT_CREATED, queryRunner, newProduct);
    return await this.getProductById(newProduct.id, queryRunner);
  }

  /**
   * Edit product
   * @param productId - ID of product to edit
   * @param editProductDto - Edit product DTO
   * @param excludeLineIds - IDs of the CI lines that should not be updated with the product's HS code and UOM
   * @param queryRunner - Query runner
   * @returns Edited product
   */
  async editProduct(
    productId: number,
    editProductDto: EditProductDto,
    excludeLineIds?: Array<number>,
    queryRunner?: QueryRunner
  ) {
    let product = await this.editAndValidateProduct(productId, editProductDto, queryRunner);

    if (
      await this.isProductExists(
        product.customsCountry,
        product.partNumber,
        product.vendor?.id,
        product.origin?.id,
        product.sku,
        product.upc,
        product.id,
        product.organization?.id
      )
    )
      throw new BadRequestException("Product already exists");
    product = await this.productRepository.save(product);

    // Invalidating matching histories of the product
    await this.matchingRuleService.invalidateMatchingHistories(
      MatchingRuleDestinationDatabaseTable.PRODUCT,
      null,
      [product.id],
      queryRunner
    );

    // Updating CI lines' HS code with the product's HS code
    await this.commercialInvoiceLineSyncService.syncLineHsCodeWithProduct(
      product.id,
      excludeLineIds,
      queryRunner
    );

    this.transactionalEventEmitter.enqueueEvent(ProductEvent.PRODUCT_UPDATED, queryRunner, product);

    return await this.getProductById(product.id, queryRunner);
  }

  async deleteProduct(productId: number) {
    const product = await this.getProductById(productId);
    if (!product) throw new NotFoundException("Product not found");
    if (product.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException("Product does not belong to the organization of current user");

    const referenceShipmentCount = await this.getProductReferenceShipmentCount(productId);
    if (referenceShipmentCount > 0) {
      throw new BadRequestException("Product is currently in use");
    }

    await this.productRepository.delete({ id: productId });
    await this.matchingRuleService.invalidateMatchingHistories(
      MatchingRuleDestinationDatabaseTable.PRODUCT,
      null,
      [productId]
    );
    return;
  }

  async batchUpdateProducts(
    batchUpdateProductsDto: BatchUpdateProductsDto
  ): Promise<BatchUpdateProductsResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedProductIds: Array<number> = [];
    try {
      const productRepository = queryRunner.manager.getRepository(Product);
      const { create: createProducts, edit: editProducts, delete: deleteProducts } = batchUpdateProductsDto;

      const invalidateHistoryProductIds: Array<number> = [];
      const updateLineHsCodeProductIds: Array<number> = [];
      const toBeSavedProducts: Array<Product> = [];
      for (let i = 0; i < (createProducts || []).length; i++) {
        const createProductDto = createProducts[i];
        const errorMsgPrefix = `Error at create list index ${i}: `;
        if (!createProductDto.customsCountry) createProductDto.customsCountry = CustomsCountry.CANADA;
        const sku = createProductDto.sku || null;
        const upc = createProductDto.upc || null;
        const { customsCountry, partNumber, vendorId, originId } = createProductDto;
        if (
          (await this.isProductExists(
            customsCountry,
            partNumber,
            vendorId,
            originId,
            sku,
            upc,
            null,
            this.request?.user?.organization?.id,
            queryRunner
          )) ||
          toBeSavedProducts.some(
            (p) =>
              p.customsCountry === customsCountry &&
              p.partNumber === partNumber &&
              p.sku === sku &&
              p.upc === upc &&
              p.vendor.id === vendorId &&
              p.origin.id === originId &&
              p.organization.id === (this.request?.user?.organization?.id || -1)
          )
        )
          throw new BadRequestException(`${errorMsgPrefix}Product already exists`);

        const newProduct = await this.createAndValidateProduct(createProductDto, queryRunner, errorMsgPrefix);
        newProduct.productType = ProductType.REGULAR;

        toBeSavedProducts.push(newProduct);
      }

      for (let i = 0; i < (editProducts || []).length; i++) {
        const { id: productId, ...editProductDto } = editProducts[i];
        const errorMsgPrefix = `Error at edit list index ${i}: `;
        const product = await this.editAndValidateProduct(
          productId,
          editProductDto,
          queryRunner,
          errorMsgPrefix
        );

        if (
          toBeSavedProducts.some(
            (p) =>
              p.customsCountry === product.customsCountry &&
              p.partNumber === product.partNumber &&
              p.sku === product.sku &&
              p.upc === product.upc &&
              p.vendor.id === product.vendor.id &&
              p.origin.id === product.origin.id &&
              p.organization.id === product.organization.id
          ) ||
          (await productRepository.existsBy({
            id: Not(
              In(
                toBeSavedProducts
                  .filter((p) => typeof p.id === "number")
                  .map((p) => p.id)
                  .concat([productId])
              )
            ),
            customsCountry: product.customsCountry,
            partNumber: product.partNumber,
            sku: product.sku,
            upc: product.upc,
            vendor: { id: product.vendor.id },
            origin: { id: product.origin.id },
            organization: { id: product.organization.id }
          }))
        )
          throw new BadRequestException(`${errorMsgPrefix}Product already exists`);
        toBeSavedProducts.push(product);

        invalidateHistoryProductIds.push(productId);
        updateLineHsCodeProductIds.push(productId);
      }

      for (let i = 0; i < (deleteProducts || []).length; i++) {
        const errorMsgPrefix = `Error at delete list index ${i}: `;
        const productId = deleteProducts[i];
        const product = await this.getProductById(productId, queryRunner);
        if (!product) throw new NotFoundException(`${errorMsgPrefix}Product not found`);
        if (product.organization?.id !== this.request?.user?.organization?.id)
          throw new BadRequestException(
            `${errorMsgPrefix}Product does not belong to the organization of current user`
          );
        if (toBeSavedProducts.some((p) => p.id === productId))
          throw new BadRequestException(`${errorMsgPrefix}Product is being saved`);
        invalidateHistoryProductIds.push(productId);
      }

      savedProductIds.push(...(await productRepository.save(toBeSavedProducts)).map((p) => p.id));
      if (Array.isArray(deleteProducts) && deleteProducts.length > 0)
        await productRepository.delete({ id: In(deleteProducts) });

      await this.matchingRuleService.invalidateMatchingHistories(
        MatchingRuleDestinationDatabaseTable.PRODUCT,
        null,
        invalidateHistoryProductIds,
        queryRunner
      );

      for (const productId of updateLineHsCodeProductIds)
        await this.commercialInvoiceLineSyncService.syncLineHsCodeWithProduct(productId, [], queryRunner);

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) await queryRunner.release();
    }

    return {
      products: await this.productRepository.find({
        where: { id: In(savedProductIds) },
        relations: FIND_PRODUCT_RELATIONS
      })
    };
  }

  async getRelatedCommercialInvoiceLines(
    productId: number,
    getRelatedCommercialInvoiceLinesDto: GetRelatedCommercialInvoiceLinesDto
  ): Promise<GetRelatedCommercialInvoiceLinesResponseDto> {
    const { skip, limit } = getRelatedCommercialInvoiceLinesDto;

    const product = await this.getProductById(productId);
    if (!product) throw new NotFoundException("Product not found");

    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select("cil.id", "id")
      .addSelect("ci.id", "commercialInvoiceId")
      .addSelect("s.id", "shipmentId")
      .addSelect("s.status", "status")
      .addSelect("s.modeOfTransport", "modeOfTransport")
      .addSelect("s.hblNumber", "hblNumber")
      .addSelect("s.cargoControlNumber", "cargoControlNumber")
      .addSelect("s.transactionNumber", "transactionNumber")
      .addSelect("s.etaDestination", "etaDestination")
      .addSelect("pol.name", "portOfLoading")
      .addSelect("pod.name", "placeOfDelivery")
      .addSelect("cvfdc.code", "vfd")
      .addSelect("ctc.code", "tt")
      .addSelect("cil.quantity", "quantity")
      .addSelect("cil.unitOfMeasure", "unitOfMeasure")
      .addSelect("cil.unitPrice", "unitPrice")
      .addSelect("cil.totalLineValue", "totalLineValue")
      .addSelect("ci.currency", "currency")
      .addSelect("cil.totalDutiesAndTaxes", "lineTotalDutiesAndTaxes")
      .from(CommercialInvoiceLine, "cil")
      .leftJoin("cil.vfd", "cvfdc")
      .leftJoin("cil.tt", "ctc")
      .leftJoin("cil.commercialInvoice", "ci")
      .leftJoin("ci.shipment", "s")
      .leftJoin("s.portOfLoading", "pol")
      .leftJoin("s.placeOfDelivery", "pod")
      .where('cil."productId" = :productId', { productId: product.id })
      .orderBy("cil.id", "ASC")
      .skip(typeof skip === "number" ? skip : 0)
      .take(typeof limit === "number" ? limit : 10);
    const lines = await queryBuilder.getRawMany<RelatedCommercialInvoiceLineDto>();
    const total = await queryBuilder.getCount();

    return {
      skip: typeof skip === "number" ? skip : 0,
      limit: typeof limit === "number" ? limit : 10,
      total,
      commercialInvoiceLines: lines
    };
  }
}
