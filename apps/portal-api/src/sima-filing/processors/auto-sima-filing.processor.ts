import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Logger, NotFoundException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { CustomsCountry, FIND_PRODUCT_RELATIONS, Product } from "nest-modules";
import { DataSource } from "typeorm";
import { AutoSimaFilingService } from "../auto-sima-filing.service";
import { AutoSimaFilingJob, DEFAULT_WORKER_OPTIONS, SimaFilingQueueName } from "../types";

@Processor(
  {
    name: SimaFilingQueueName.AUTO_SIMA_FILING
  },
  DEFAULT_WORKER_OPTIONS
)
export class AutoSimaFilingProcessor extends WorkerHost {
  constructor(
    @Inject(AutoSimaFilingService)
    private readonly autoSimaFilingService: AutoSimaFilingService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    super();
  }
  private readonly logger = new Logger(AutoSimaFilingProcessor.name);

  async process(job: AutoSimaFilingJob) {
    this.logger.log(`Processing auto SIMA filing job. ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { productId, sourceEventType } = job.data;

    const product = await this.dataSource.manager.findOne(Product, {
      where: { id: productId },
      relations: FIND_PRODUCT_RELATIONS
    });

    if (!product) throw new NotFoundException(`Product with ID ${productId} not found`);
    if (product.customsCountry !== CustomsCountry.CANADA) {
      this.logger.log(`Product ${product.id}'s customs country is not Canada, skipping auto SIMA filing...`);
      return;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (sourceEventType === "update") {
        job.log(`Job is from product update event, removing all existing SIMA filings...`);
        await this.autoSimaFilingService.removeGeneratedSimaFilings(product, queryRunner);
      }

      this.logger.log(`Auto filling SIMA filing for product ${product.id}...`);
      await this.autoSimaFilingService.autoFillSimaFiling(product, queryRunner);

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error processing auto SIMA filing job. ID: ${job.id}, Data: ${JSON.stringify(job.data)}, Error message: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
