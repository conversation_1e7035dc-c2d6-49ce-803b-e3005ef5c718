import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger, Type } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  CanadaAntiDumping,
  CanadaSimaCode,
  CustomsCountry,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  Product,
  RuleQueryService,
  SimaIncoterms,
  SimaSubjectCode,
  UserPermission
} from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { SimaFilingService } from "./sima-filing.service";
import { AutoSimaFilingQueue, SimaFilingQueueName } from "./types";

@Injectable()
export class AutoSimaFilingService {
  constructor(
    @InjectQueue(SimaFilingQueueName.AUTO_SIMA_FILING)
    private readonly autoSimaFilingQueue: AutoSimaFilingQueue,
    private readonly moduleRef: ModuleRef,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  private readonly logger = new Logger(AutoSimaFilingService.name);

  private async getService(organizationId: number, service: Type<any>) {
    // set org id
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.ORGANIZATION_ADMIN,
          organization: {
            id: organizationId
          }
        }
      },
      contextId
    );
    const resolved = await this.moduleRef.resolve(service, contextId, {
      strict: false
    });
    // @see: https://github.com/nestjs/nest/issues/5778
    await new Promise((resolve) => process.nextTick(resolve));
    return resolved;
  }

  async createProductSimaFiling(product: Product) {
    // await this.autoFillSimaFiling(product);
    await this.autoSimaFilingQueue.add(
      product.id.toString(),
      {
        productId: product.id,
        sourceEventType: "create"
      },
      {
        deduplication: {
          id: product.id.toString(),
          ttl: 5000 // 5 seconds
        }
      }
    );
  }

  async updateProductSimaFiling(product: Product) {
    // await this.removeGeneratedSimaFilings(product);
    // await this.autoFillSimaFiling(product);
    await this.autoSimaFilingQueue.add(
      product.id.toString(),
      {
        productId: product.id,
        sourceEventType: "update"
      },
      {
        deduplication: {
          id: product.id.toString(),
          ttl: 5000 // 5 seconds
        }
      }
    );
  }

  /**
   * Get all ongoing auto SIMA filing jobs, which includes all running and waiting jobs.
   * @param productIds IDs of products to filter the jobs
   * @returns List of ID, status and data of ongoing auto SIMA filing jobs
   */
  async getOngoingAutoSimaFilingJobs(productIds?: Array<number>) {
    const jobs = await this.autoSimaFilingQueue.getJobs(
      ["waiting-children", "waiting", "delayed", "active", "prioritized", "paused", "repeat", "wait"],
      0
    );
    return await Promise.all(
      jobs
        .filter((job) => (Array.isArray(productIds) ? productIds.includes(job.data.productId) : true))
        .map(async (job) => ({
          jobId: job.id,
          status: await job.getState(),
          data: job.data
        }))
    );
  }

  async getMatchingRuleService(organizationId: number): Promise<MatchingRuleService> {
    return await this.getService(organizationId, MatchingRuleService);
  }

  async removeGeneratedSimaFilings(product: Product, queryRunner?: QueryRunner) {
    const matchingRuleService = await this.getMatchingRuleService(product.organization.id);

    const matchingRules = await matchingRuleService.getMatchingRules(
      {
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        destinationId: product.id,
        sourceTable: MatchingRuleSourceDatabaseTable.SIMA_FILING
      },
      queryRunner
    );

    const autoCreatedMatchingRules = matchingRules.matchingRules.filter((rule) =>
      rule.conditions.every(
        (condition) => condition.attribute === "id" || condition.attribute === "organizationId"
      )
    );

    for (const matchingRule of autoCreatedMatchingRules) {
      await matchingRuleService.updateMatchingRuleStatus(
        matchingRule.id,
        {
          status: MatchingRuleStatus.DISABLED
        },
        queryRunner
      );
      await matchingRuleService.deleteMatchingRule(matchingRule.id, queryRunner);
    }

    // TODO: Delete all auto created SIMA filings
  }

  async autoFillSimaFiling(product: Product, queryRunner?: QueryRunner) {
    if (product.customsCountry !== CustomsCountry.CANADA) {
      this.logger.log(`Product ${product.id}'s customs country is not Canada, skipping auto SIMA filing...`);
      return;
    }
    const simaRules = await this.getApplicableSimaRulesForProduct(product, queryRunner);

    if (simaRules.length === 0) {
      return;
    }

    if (simaRules.length > 1) {
      return;
    }

    const simaRule = simaRules[0];

    const service: SimaFilingService = await this.getService(product.organization.id, SimaFilingService);

    const matchingRuleService: MatchingRuleService = await this.getService(
      product.organization.id,
      MatchingRuleService
    );

    const repository = (queryRunner?.manager ?? this.dataSource).getRepository(CanadaSimaCode);

    const simaCode = await repository.findOne({
      where: {
        code: 52
      }
    });

    if (!simaCode) {
      this.logger.error("SIMA code 52 not found in the database");
      return;
    }

    const response = await service.createSimaFilingAndMatchingRule(
      {
        matchingRule: {
          name: `SimaFiling - 52 - product id equals ${product.id}`,
          destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT
        },
        simaFiling: {
          subjectCode: SimaSubjectCode.SUBJECT,
          incoterms: SimaIncoterms.FOB,
          simaCodeId: simaCode.id,
          measureInForceId: simaRule.id,
          security: false
        },
        matchingConditions: [
          {
            attribute: "id",
            isOperationInverted: false,
            operator: MatchingConditionOperator.EQUALS,
            value: product.id.toString()
          }
        ]
      },
      queryRunner
    );

    const matchingRuleId = response.matchingRule.id;

    // activate the matching rule
    await matchingRuleService.updateMatchingRuleStatus(
      matchingRuleId,
      {
        status: MatchingRuleStatus.ACTIVE
      },
      queryRunner
    );
  }

  async getApplicableSimaRulesForProduct(
    product: Product,
    queryRunner?: QueryRunner
  ): Promise<CanadaAntiDumping[]> {
    const ruleQueryService = await this.getService(product.organization.id, RuleQueryService);

    const queryResult = await ruleQueryService.queryMatchingResults(
      {
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        destinationId: product.id,
        sourceTables: [
          MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
          MatchingRuleSourceDatabaseTable.SIMA_FILING
        ]
      },
      queryRunner
    );

    const canadaAntiDumpingMatchingResults = queryResult.matchingResults.find(
      (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
    );

    const existingSimaFilings = queryResult.matchingResults.find(
      (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING
    );

    if (existingSimaFilings.sourceRecords.length > 0) {
      return [];
    }

    if (!canadaAntiDumpingMatchingResults) {
      return [];
    }

    return canadaAntiDumpingMatchingResults.sourceRecords;
  }
}
