import {
  BatchDocumentExtractionErrorEvent,
  BatchDocumentExtractionStartedEvent,
  FileBatchEvent
} from "@/document/events/file-batch.event";
import { FileBatchService } from "@/document/services/file-batch.service";
import { InjectQueue, OnQueueEvent, QueueEventsHost, QueueEventsListener } from "@nestjs/bullmq";
import { Inject } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Queue as BullQueue } from "bullmq";
import { FileBatchStatus } from "nest-modules";
import { Queue } from "../constants";
import { FileBatchJob } from "../file-batch.processor";

@QueueEventsListener(Queue.FILE_BATCH_TASKS)
export class FileBatchQueueListener extends QueueEventsHost {
  constructor(
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService,
    @InjectQueue(Queue.FILE_BATCH_TASKS)
    private readonly queue: BullQueue
  ) {
    super();
  }

  @OnQueueEvent("active")
  async onActive(args: { jobId: string }, id: string) {
    const job: FileBatchJob = await this.queue.getJob(args.jobId);

    await this.fileBatchService.updateFileBatchStatus(job.data.fileBatchId, FileBatchStatus.EXTRACTING);

    this.eventEmitter.emit(
      FileBatchEvent.DOCUMENT_EXTRACTION_STARTED,
      new BatchDocumentExtractionStartedEvent(job.data.fileBatchId, job.data.organizationId)
    );
  }

  @OnQueueEvent("failed")
  async onFailed(args: { jobId: string; failedReason: string }, id: string) {
    const batch = await this.fileBatchService.getFileBatch(args.jobId);
    const job: FileBatchJob = await this.queue.getJob(args.jobId);

    const error = args.failedReason;

    const documentIds = job.data.documents.map((document) => document.id);

    this.eventEmitter.emit(
      FileBatchEvent.DOCUMENT_EXTRACTION_ERROR,
      new BatchDocumentExtractionErrorEvent(
        job.data.fileBatchId,
        job.data.organizationId,
        batch.creator,
        documentIds,
        error
      )
    );
  }
}
