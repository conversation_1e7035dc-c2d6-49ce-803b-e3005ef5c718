import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Logger } from "@nestjs/common";
import { Job, WaitingChildrenError } from "bullmq";
import { DocumentStatus, DocumentTypeService, File, FileStatus } from "nest-modules";
import { DocumentService } from "../services/document.service";
import { FileService } from "../services/file.service";
import { Queue as DocumentQueue } from "./constants";

import { FileCorruptedError, ParserError, ParseResult, XlsxParserService } from "../services";

import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { DocumentExtractionTaskDispatcher, DocumentJobResult } from "../document-extraction";
import { DocumentSplitResultDto } from "../dto/document.dto";
import { DocumentTypeMapper } from "../services/mapper/document-type.mapper";
import { ClassificationTask } from "../services/openai/classification.task";
import { ParserService } from "../services/parsers/parser.service";
import { LlamaParseResultReader } from "../services/readers/llama-parse-result.reader";

export interface FileProcessorJobData {
  currentStep?: FileProcessorSteps;
  fileId: number;
  organizationId: number;
  shipmentId?: number;
  batchId?: string;
}

export type FileProcessorJobResult = DocumentJobResult[];

export type FileProcessorJob = Job<FileProcessorJobData, FileProcessorJobResult, string>;

export enum FileProcessorSteps {
  PARSE,
  SPLIT,
  EXTRACT_DOCUMENTS,
  WAIT_ALL_DOCUMENTS_EXTRACTED,
  ALL_DOCUMENTS_EXTRACTED,
  FINISH
}

function RestartOnError(_: Object, __: string | Symbol, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;
  descriptor.value = async function (job: FileProcessorJob) {
    try {
      return await originalMethod.apply(this, [job]);
    } catch (error) {
      if (error instanceof WaitingChildrenError) {
        // clear attempts
        throw error;
      }

      // roll back to initial step
      await this.updateData(job, {
        currentStep: FileProcessorSteps.PARSE
      });

      throw error;
    }
  };
}

@Processor(
  {
    name: DocumentQueue.FILE_TASKS
  },
  {
    concurrency: parseInt(process.env.FILE_PROCESSOR_CONCURRENCY || "1", 10)
  }
)
export class FileProcessor extends WorkerHost {
  private readonly logger = new Logger(FileProcessor.name);

  constructor(
    @Inject(FileService)
    private readonly fileService: FileService,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(DocumentTypeService)
    private readonly documentTypeService: DocumentTypeService,
    @Inject(ParserService)
    private readonly parserService: ParserService,
    @Inject(XlsxParserService)
    private readonly xlsxParserService: XlsxParserService,
    @Inject(ClassificationTask)
    private readonly classificationTask: ClassificationTask,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(DocumentExtractionTaskDispatcher)
    private readonly documentExtractionTaskDispatcher: DocumentExtractionTaskDispatcher
  ) {
    super();
  }

  private async waitChildren(job: FileProcessorJob, token: string) {
    const shouldWait = await job.moveToWaitingChildren(token);
    if (shouldWait) {
      throw new WaitingChildrenError();
    }
  }

  /**
   * Dispatching extraction tasks for documents
   *
   * @param job The job
   * @param splitResult The split result
   */
  private async dispatchDocumentExtractionTasks(
    job: FileProcessorJob,
    splitResult: { id: number; name: string }[]
  ) {
    const skippedDocumentTypes = ["other", "unknown"];

    const documents = {
      skipped: [],
      pending: []
    };

    splitResult.forEach((doc) => {
      if (skippedDocumentTypes.includes(doc.name)) {
        documents.skipped.push(doc);
      } else {
        documents.pending.push(doc);
      }
    });

    job.log(
      `Processing ${splitResult.length} documents, ${documents.skipped.length} skipped, ${documents.pending.length} pending`
    );

    // update status to extracted for unknown documents
    await Promise.all(
      documents.skipped.map(async (doc) => {
        await this.documentService.updateDocumentStatus(doc.id, DocumentStatus.EXTRACTED);
      })
    );

    await Promise.all(
      documents.pending.map(async (doc) => {
        await this.documentExtractionTaskDispatcher.processDocument(
          {
            id: doc.id,
            documentType: doc.name,
            organizationId: job.data.organizationId,
            batchId: job.data.batchId,
            model: "gpt-4o"
          },
          {
            ignoreDependencyOnFailure: true,
            parent: {
              id: job.id,
              queue: job.queueQualifiedName
            }
          }
        );
      })
    );
  }

  private async updateData(job: FileProcessorJob, data: Partial<FileProcessorJobData>) {
    await job.updateData({
      ...job.data,
      ...data
    });
  }

  async process(job: FileProcessorJob, token: string): Promise<any> {
    this.logger.log("Processing file id: " + job.data.fileId + " with task: " + job.name);

    let step = job.data.currentStep ?? 0;
    let splitResult: { id: number; name: string }[] = [];

    while (step !== FileProcessorSteps.FINISH) {
      job.log(`Processing step ${step} ${FileProcessorSteps[step]}`);

      switch (step) {
        case FileProcessorSteps.PARSE:
          await this.processParse(job);
          break;

        case FileProcessorSteps.SPLIT:
          splitResult = await this.processSplit(job);
          break;

        case FileProcessorSteps.EXTRACT_DOCUMENTS:
          await this.dispatchDocumentExtractionTasks(job, splitResult);
          break;

        case FileProcessorSteps.WAIT_ALL_DOCUMENTS_EXTRACTED:
          job.log("Waiting for all documents to be extracted...");
          await this.waitChildren(job, token);
          break;

        case FileProcessorSteps.ALL_DOCUMENTS_EXTRACTED:
          job.log("All documents extracted");
          // log failed children
          job.log("Failed children: " + JSON.stringify(await job.getFailedChildrenValues()));
          job.log("Children: " + JSON.stringify(await job.getChildrenValues()));

          // return all children
          return Object.values(await job.getChildrenValues());

        default:
          this.logger.error("Unknown job type");
          throw new Error("Unknown job type");
      }

      ++step;
      await this.updateData(job, {
        currentStep: step
      });
    }
  }

  @RestartOnError
  private async processParse(job: FileProcessorJob): Promise<File> {
    this.updateFileStatus(job.data.fileId, FileStatus.PARSING);

    try {
      const { path, mimeType } = await this.fileService.getFileInfo(job.data.fileId);
      job.log("Retrieving file parse result");
      let parseResult: ParseResult;
      if (
        [
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.ms-excel"
        ].includes(mimeType)
      ) {
        parseResult = await this.xlsxParserService.getParseResult(path);
      } else {
        parseResult = await this.parserService.getParseResult(path);
      }

      job.log("Updating file extraction result");
      await this.fileService.updateFileParseResult(job.data.fileId, parseResult);

      // await this.fileTaskDispatcher.split(job.data);
      return;
    } catch (error) {
      job.log(`Failed to parse file, attempts: ${job.attemptsStarted}/${job.opts.attempts}`);
      job.log(error.message);
      // do not update file status if still has remaining attempts
      if (job.attemptsStarted < job.opts.attempts) {
        throw error;
      }

      if (error instanceof FileCorruptedError) {
        await this.updateFileStatus(job.data.fileId, FileStatus.FILE_CORRUPTED);
      } else if (error instanceof ParserError) {
        await this.updateFileStatus(job.data.fileId, FileStatus.PARSE_FAILED);
      } else {
        await this.updateFileStatus(job.data.fileId, FileStatus.ERROR);
      }
      throw error;
    }
  }

  @RestartOnError
  private async processSplit(job: FileProcessorJob): Promise<any> {
    this.updateFileStatus(job.data.fileId, FileStatus.SPLITTING);

    try {
      const file = await this.fileService.getFile(job.data.fileId);
      await this.documentService.deleteDocumentsByFileId(file.id);

      const documentTypeNameIdPairs = await this.documentTypeService
        .getAllDocumentTypes()
        .then(DocumentTypeMapper.mapToNameIdPair);

      const pages = LlamaParseResultReader.load(JSON.parse(file.parseResult)).getPages();

      const splitResult = (await this.classificationTask.execute({
        pages
      })) as DocumentSplitResultDto;

      job.log(JSON.stringify(splitResult));

      if (splitResult.documents.length === 0) {
        await this.updateFileStatus(job.data.fileId, FileStatus.NO_DOCUMENT_DETECTED);
        return;
      }

      // associate with db records
      splitResult.fileId = file.id;
      splitResult.batchId = file.batchId;
      splitResult.documents.forEach((doc) => {
        doc.typeId = documentTypeNameIdPairs[doc.type] ?? documentTypeNameIdPairs["OTHER"];
        doc.organizationId = job.data.organizationId;
      });

      job.log(JSON.stringify(splitResult));

      // save to db
      const documents = await this.documentService.createDocumentsFromSplitResult(
        splitResult,
        job.data.shipmentId
      );

      // spawn next job
      await this.updateFileStatus(job.data.fileId, FileStatus.PROCESSED);

      return documents.map((doc) => ({
        id: doc.id,
        name: doc.name
      }));
    } catch (error) {
      job.log(`Failed to split file, attempts: ${job.attemptsStarted}/${job.opts.attempts}`);
      job.log(error.message);
      // do not update file status if still has remaining attempts
      if (job.attemptsStarted < job.opts.attempts) {
        throw error;
      }

      this.logger.error(error.message, error.stack);
      await this.updateFileStatus(job.data.fileId, FileStatus.SPLIT_FAILED);
      throw error;
    }
  }

  /**
   * Update file status
   *
   * @param fileId The ID of the file to update
   * @param status The new status of the file
   */
  private async updateFileStatus(fileId: number, status: FileStatus): Promise<void> {
    try {
      await this.fileService.updateFileStatus(fileId, status);
    } catch (error) {
      this.logger.error(`Error updating file status to ${status}`, error);
    }
  }
}
