import { Injectable, Logger, MessageEvent } from "@nestjs/common";
import { QueueEvents, QueueEventsProducer } from "bullmq";
import { fromEvent, map, Observable } from "rxjs";

type ChannelMessageEvent = {
  eventName: string;
  type: string;
  data: any;
};

@Injectable()
export class SseEventService {
  private readonly QUEUE_NAME = "sse-events";
  private readonly queueEventsProducer: QueueEventsProducer;
  private readonly queueEvents: QueueEvents;
  private readonly logger = new Logger(SseEventService.name);

  constructor() {
    if (!process.env.REDIS_HOST || !process.env.REDIS_PORT) {
      throw new Error("REDIS_HOST and REDIS_PORT must be set");
    }

    try {
      const connection = {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT)
      };

      this.queueEventsProducer = new QueueEventsProducer(this.QUEUE_NAME, {
        connection
      });

      this.queueEvents = new QueueEvents(this.QUEUE_NAME, {
        connection
      });
    } catch (error) {
      this.logger.error(`Failed to connect to Redis: ${error.message}`);
    }
  }

  /**
   * Send an SSE event
   *
   * @param channel The channel to send the event to
   * @param eventName The name of the event
   * @param data The data to send
   */
  async send(channel: string, eventName: string, data: any) {
    try {
      await this.queueEventsProducer.publishEvent<ChannelMessageEvent>({
        // We use channel as event name so that we can listen to a specific channel
        eventName: channel,
        type: eventName,
        data: JSON.stringify(data)
      });
    } catch (error) {
      this.logger.error(`Failed to send SSE event: ${error.message}`, {
        channel,
        eventName
      });
    }
  }

  /**
   * Listen to an SSE event
   *
   * @param channel The channel to listen to
   * @returns An observable that emits the event
   */
  listen(channel: string): Observable<MessageEvent> {
    // @see https://github.com/ReactiveX/rxjs/issues/7401
    return fromEvent<[ChannelMessageEvent, string]>(this.queueEvents, channel).pipe(
      map(([event, id]) => {
        return {
          id,
          type: event.type,
          data: event.data
        } as MessageEvent;
      })
    );
  }
}
