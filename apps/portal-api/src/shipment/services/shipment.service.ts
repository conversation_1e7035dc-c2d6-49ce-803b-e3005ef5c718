import { DocumentService } from "@/document/services/document.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CandataRNSResponseDto,
  CandataService,
  CandataShipmentDto,
  CommercialInvoiceLine,
  Container,
  convertFromCamelCase,
  CreateShipmentDto,
  CustomsCountry,
  DutySummaryLineDto,
  EditShipmentDto,
  FIND_CONTAINER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  getFindOptions,
  GetShipmentCustomsActivitiesResponseDto,
  GetShipmentDutySummaryResponseDto,
  GetShipmentsDto,
  GetShipmentsResponseDto,
  ImporterColumn,
  ImporterStatus,
  Location,
  RNSProcessingIndicator,
  Shipment,
  SHIPMENT_ENUM_KEYS,
  SHIPMENT_REQUIRED_KEYS,
  ShipmentColumn,
  ShipmentContainerDto,
  SortOrder,
  TrackingStatus,
  UserPermission,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import {
  Between,
  DataSource,
  FindOptionsWhere,
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Not,
  QueryRunner,
  Raw,
  Repository
} from "typeorm";
import { ImporterService } from "../../importer/importer.service";
import { LocationService } from "../../location/location.service";
import { TradePartnerService } from "../../trade-partner/trade-partner.service";
import {
  ShipmentAttemptEditSubmissionMandatoryFields,
  ShipmentChangedFieldDto,
  ShipmentCreatedEventDto,
  ShipmentDeletedEventDto,
  ShipmentEditedEventDto
} from "../dto/event.dto";
import { ShipmentEnricher } from "../enrichers/shipment.enricher";
import { ShipmentEvent } from "../types/event.types";
import { NON_EDITABLE_FIELDS_POST_SUBMISSION } from "../types/shipment.types";
import { ComplianceValidationService } from "./compliance-validation.service";
import { EntrySubmissionService } from "./entry-submission.service";

@Injectable({ scope: Scope.REQUEST })
export class ShipmentService {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Container)
    private readonly containerRepository: Repository<Container>,
    @InjectRepository(CommercialInvoiceLine)
    private readonly commercialInvoiceLineRepository: Repository<CommercialInvoiceLine>,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => LocationService))
    private readonly locationService: LocationService,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(ImporterService)
    private readonly importerService: ImporterService,
    @Inject(forwardRef(() => DocumentService))
    private readonly documentService: DocumentService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
    @Inject(ShipmentEnricher)
    private readonly enricher: ShipmentEnricher
  ) {}

  private readonly logger = new Logger(ShipmentService.name);

  /**
   * Validate and assign documents
   *
   * @deprecated this code may be removed in the future
   */
  private async validateAndAssignDocuments(
    documentIds: number[] | null | undefined,
    propertyName: string,
    shipment: Shipment
  ) {
    if (documentIds) {
      shipment[propertyName] = (await this.documentService.getAllDocumentByIds(documentIds)).map((doc) => ({
        id: doc.id
      }));
      // TODO: Check if any documents are not found
      if (!shipment[propertyName])
        throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
      // TODO: validate that all documents belong to the organization
    } else shipment[propertyName] = null;
  }

  private async validateAndAssignLocation(
    locationId: number | null | undefined,
    propertyName: string,
    shipment: Shipment,
    queryRunner?: QueryRunner
  ) {
    if (typeof locationId === "number") {
      shipment[propertyName] = await this.locationService.getLocationById(locationId, queryRunner);
      if (!shipment[propertyName])
        throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
    } else shipment[propertyName] = null;
  }

  private async validateAndAssignImporter(
    importerId: number | null | undefined,
    propertyName: string,
    shipment: Shipment,
    queryRunner?: QueryRunner
  ) {
    if (typeof importerId === "number") {
      shipment[propertyName] = await this.importerService.getImporterById(importerId, queryRunner);
      if (!shipment[propertyName])
        throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
      if (shipment[propertyName].organization?.id !== this.request?.user?.organization?.id)
        throw new BadRequestException(
          `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
        );
    } else shipment[propertyName] = null;
  }

  private async validateAndAssignTradePartner(
    tradePartnerId: number | null | undefined,
    propertyName: string,
    shipment: Shipment,
    queryRunner?: QueryRunner
  ) {
    if (typeof tradePartnerId === "number") {
      shipment[propertyName] = await this.tradePartnerService.getTradePartnerById(
        tradePartnerId,
        queryRunner
      );
      if (!shipment[propertyName])
        throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
      if (
        shipment[propertyName]?.organization &&
        shipment[propertyName].organization?.id !== this.request?.user?.organization?.id
      )
        throw new BadRequestException(
          `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
        );
    } else shipment[propertyName] = null;
  }

  private async isCustomsCountrySameAsLocationCountry(
    customsCountry: CustomsCountry,
    locationId: number,
    queryRunner?: QueryRunner
  ) {
    const location = await this.locationService.getLocationById(locationId, queryRunner);
    if (!location) throw new NotFoundException(`Location ${locationId} not found`);
    return location.country?.alpha2?.toLowerCase() === customsCountry.toLowerCase();
  }

  private async hblNumberExists(hblNumber: string, id?: number, queryRunner?: QueryRunner) {
    const where: FindOptionsWhere<Shipment> = { hblNumber };

    if (id) {
      where.id = Not(id);
    }

    if (this.request?.user?.organization?.id) {
      where.organization = { id: this.request?.user?.organization?.id };
    }

    return await (
      queryRunner ? queryRunner.manager.getRepository(Shipment) : this.shipmentRepository
    ).existsBy(where);
  }

  private async setOrganizationImporter(shipment: Shipment, queryRunner?: QueryRunner) {
    const { importers } = await this.importerService.getImporters(
      {
        status: ImporterStatus.ACTIVE,
        organizationId: this.request?.user?.organization?.id,
        skip: 0,
        limit: 1,
        sortBy: ImporterColumn.createDate,
        sortOrder: SortOrder.ASC
      },
      queryRunner
    );
    if (importers.length > 0) shipment.importer = importers[0];
  }

  async recalculateShipmentDatesAndStatus(shipments: Array<Shipment>, queryRunner?: QueryRunner) {
    const containers = await (
      queryRunner ? queryRunner.manager.getRepository(Container) : this.containerRepository
    ).find({
      where: { shipment: { id: In(shipments.map((s) => s.id)) } },
      relations: FIND_CONTAINER_RELATIONS
    });
    this.logger.log(
      `Found ${containers.length} containers for ${shipments.length} shipments in recalculate shipment dates`
    );

    for (const shipment of shipments) {
      const shipmentContainers = containers.filter((c) => c.shipment?.id === shipment.id);
      this.logger.log(`Shipment ${shipment.id} has ${shipmentContainers.length} containers`);
      if (shipmentContainers.length === 0) continue;

      const updateResult = await (
        queryRunner ? queryRunner.manager.getRepository(Shipment) : this.shipmentRepository
      ).update(
        { id: shipment.id },
        {
          status: shipmentContainers.reduce(
            (status, c) => (c.trackingStatus === TrackingStatus.ONLINE && !status ? c.status : status),
            shipment.status
          ),
          etaDestination: shipmentContainers.reduce(
            (eta, c) => (!eta ? c.etaDestination : eta),
            null as Date | null
          ),
          pickupLfd: shipmentContainers.reduce(
            (pickupLfd, c) => (!pickupLfd ? c.pickupLfd : pickupLfd),
            null as Date | null
          ),
          pickupDate: shipmentContainers.reduce(
            (pickupDate, c) => (!pickupDate ? c.pickupDate : pickupDate),
            null as Date | null
          ),
          returnLfd: shipmentContainers.reduce(
            (returnLfd, c) => (!returnLfd ? c.returnLfd : returnLfd),
            null as Date | null
          ),
          returnDate: shipmentContainers.reduce(
            (returnDate, c) => (!returnDate ? c.returnDate : returnDate),
            null as Date | null
          )
        }
      );
      this.logger.debug(
        `Updated shipment dates for shipment ${shipment.id}: ${updateResult.affected} rows affected`
      );
    }
  }

  async getShipments(getShipmentsDto: GetShipmentsDto): Promise<GetShipmentsResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getShipmentsDto.organizationId = this.request?.user?.organization?.id || -1;
    const {
      status,
      trackingStatus,
      containerNumber,
      containerType,
      etaDestinationFrom,
      etaDestinationTo,
      pickupLfdFrom,
      pickupLfdTo,
      pickupDateFrom,
      pickupDateTo,
      returnLfdFrom,
      returnLfdTo,
      returnDateFrom,
      returnDateTo,
      ...dto
    } = getShipmentsDto;
    const { where, order, skip, take } = getFindOptions<Shipment>(
      dto,
      SHIPMENT_ENUM_KEYS,
      [],
      ShipmentColumn.id
    );
    const containerWhere: FindOptionsWhere<Container> = {};
    if (status) containerWhere.status = status;
    if (trackingStatus) containerWhere.trackingStatus = trackingStatus;
    if (containerNumber)
      containerWhere.containerNumber = Raw(
        (alias) =>
          `LOWER(${alias}) LIKE LOWER('%' || replace(replace(replace(:containerNumber, '\\', '\\\\'), '%', '\\%'), '_', '\\_') || '%')`,
        { containerNumber }
      );
    if (containerType) containerWhere.containerType = containerType;
    if (etaDestinationFrom || etaDestinationTo) {
      const query =
        etaDestinationFrom && etaDestinationTo
          ? Between(etaDestinationFrom, etaDestinationTo)
          : etaDestinationFrom
            ? MoreThanOrEqual(etaDestinationFrom)
            : LessThanOrEqual(etaDestinationTo);
      where.etaDestination = query;
      containerWhere.etaDestination = query;
    }
    if (pickupLfdFrom || pickupLfdTo) {
      const query =
        pickupLfdFrom && pickupLfdTo
          ? Between(pickupLfdFrom, pickupLfdTo)
          : pickupLfdFrom
            ? MoreThanOrEqual(pickupLfdFrom)
            : LessThanOrEqual(pickupLfdTo);
      containerWhere.pickupLfd = query;
      where.pickupLfd = query;
    }
    if (pickupDateFrom || pickupDateTo) {
      const query =
        pickupDateFrom && pickupDateTo
          ? Between(pickupDateFrom, pickupDateTo)
          : pickupDateFrom
            ? MoreThanOrEqual(pickupDateFrom)
            : LessThanOrEqual(pickupDateTo);
      containerWhere.pickupDate = query;
      where.pickupDate = query;
    }
    if (returnLfdFrom || returnLfdTo) {
      const query =
        returnLfdFrom && returnLfdTo
          ? Between(returnLfdFrom, returnLfdTo)
          : returnLfdFrom
            ? MoreThanOrEqual(returnLfdFrom)
            : LessThanOrEqual(returnLfdTo);
      containerWhere.returnLfd = query;
      where.returnLfd = query;
    }
    if (returnDateFrom || returnDateTo) {
      const query =
        returnDateFrom && returnDateTo
          ? Between(returnDateFrom, returnDateTo)
          : returnDateFrom
            ? MoreThanOrEqual(returnDateFrom)
            : LessThanOrEqual(returnDateTo);
      containerWhere.returnDate = query;
      where.returnDate = query;
    }

    if (Object.keys(containerWhere).length > 0) where.containers = containerWhere;

    const [shipments, total] = await this.shipmentRepository.findAndCount({
      where,
      relations: FIND_SHIPMENT_RELATIONS,
      order,
      skip,
      take
    });

    return {
      shipments,
      total,
      skip,
      limit: take
    };
  }

  /**
   * Get a shipment by its identifier.
   *
   * @returns The shipment object.
   */
  async getShipmentByIdentifier({
    hblNumber,
    cargoControlNumber
  }: {
    hblNumber?: string;
    cargoControlNumber?: string;
  }): Promise<Shipment | null> {
    if (!hblNumber && !cargoControlNumber) {
      throw new BadRequestException("Either hblNumber or cargoControlNumber must be provided");
    }

    const where: Record<string, string> = {};
    if (hblNumber) where.hblNumber = hblNumber;
    if (cargoControlNumber) where.cargoControlNumber = cargoControlNumber;

    // scope to organization of current user
    where.organization = { id: this.request?.user?.organization?.id || -1 } as any;

    return await this.shipmentRepository.findOne({ where });
  }

  async getShipmentById(shipmentId: number, queryRunner?: QueryRunner) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(Shipment) : this.shipmentRepository
    ).findOne({
      where: {
        id: shipmentId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_SHIPMENT_RELATIONS
    });
  }

  async createShipment(createShipmentDto: CreateShipmentDto, queryRunner?: QueryRunner) {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      let newShipment = new Shipment();
      newShipment.organization = this.request?.user?.organization;
      newShipment.createdBy = this.request?.user || null;
      newShipment.lastEditedBy = this.request?.user || null;

      if (await this.hblNumberExists(createShipmentDto.hblNumber, undefined, tQueryRunner)) {
        throw new BadRequestException(`HBL number ${createShipmentDto.hblNumber} already exists`);
      }

      const toBeCreatedContainers: Array<Partial<Container>> = [];
      for (const [key, value] of Object.entries(createShipmentDto)) {
        if (value === undefined) continue;
        const propertyName = key.replace(/Id(s?)$/, "$1");
        switch (propertyName) {
          case "containers":
            if (Array.isArray(value) && value.every((c) => c instanceof ShipmentContainerDto))
              toBeCreatedContainers.push(
                ...value.map((c) => ({
                  ...c,
                  createdBy: this.request?.user || null,
                  lastEditedBy: this.request?.user || null
                }))
              );
            break;
          case "portOfDischarge":
          case "portOfLoading":
          case "placeOfDelivery":
            await this.validateAndAssignLocation(value, propertyName, newShipment, tQueryRunner);
            break;
          case "importer":
            await this.validateAndAssignImporter(value, propertyName, newShipment, tQueryRunner);
            break;
          case "carrier":
          case "manufacturer":
          case "shipper":
          case "consignee":
          case "forwarder":
          case "trucker":
          case "pickupLocation":
            await this.validateAndAssignTradePartner(value, propertyName, newShipment, tQueryRunner);
            break;
          case "documents":
            await this.validateAndAssignDocuments(value, propertyName, newShipment);
            break;
          default:
            if (!(SHIPMENT_REQUIRED_KEYS.includes(propertyName) && [null, undefined].includes(value)))
              newShipment[propertyName] = value;
            break;
        }
      }
      if (!newShipment.importer) await this.setOrganizationImporter(newShipment, tQueryRunner);
      if (!newShipment.customsCountry) newShipment.customsCountry = CustomsCountry.CANADA;

      // Check if shipment's customs country is the same as place of delivery/port of discharge's country
      for (const field of ["placeOfDelivery", "portOfDischarge"]) {
        if (
          newShipment[field] &&
          !(await this.isCustomsCountrySameAsLocationCountry(
            newShipment.customsCountry,
            newShipment[field]?.id,
            tQueryRunner
          ))
        ) {
          throw new BadRequestException(`Customs country must be the same as ${field}'s country`);
        }
      }

      newShipment = await tQueryRunner.manager.save(newShipment);
      if (toBeCreatedContainers.length > 0) {
        const newContainers = await tQueryRunner.manager.save(
          toBeCreatedContainers.map((c) => {
            const newContainer = new Container();
            Object.assign(newContainer, c);
            newContainer.shipment = newShipment;
            return newContainer;
          })
        );
        this.logger.debug(`Created new containers: ${JSON.stringify(newContainers.map((c) => c.id))}`);

        await this.recalculateShipmentDatesAndStatus([newShipment], tQueryRunner);
      }

      this.eventEmitter.emit(
        ShipmentEvent.SHIPMENT_CREATED,
        new ShipmentCreatedEventDto(newShipment.id, tQueryRunner, this.request?.user, createShipmentDto)
      );

      if (!queryRunner) await tQueryRunner.commitTransaction();

      return await this.getShipmentById(newShipment.id, queryRunner);
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async editShipment(shipmentId: number, editShipmentDto: EditShipmentDto, queryRunner?: QueryRunner) {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      const shipment = await this.getShipmentById(shipmentId, tQueryRunner);
      if (!shipment) throw new NotFoundException("Shipment not found");

      if (
        editShipmentDto.hblNumber &&
        (await this.hblNumberExists(editShipmentDto.hblNumber, shipmentId, tQueryRunner))
      ) {
        throw new BadRequestException("HBL number must be unique");
      }

      const changedMandatoryFields = NON_EDITABLE_FIELDS_POST_SUBMISSION.filter(
        (field) => editShipmentDto[field] !== undefined && shipment[field] !== editShipmentDto[field]
      ).map((field) => new ShipmentChangedFieldDto(field, shipment[field], editShipmentDto[field]));

      if (
        this.complianceValidationService.isShipmentSubmitted(shipment) &&
        changedMandatoryFields.length > 0
      ) {
        this.eventEmitter.emit(
          ShipmentEvent.SHIPMENT_ATTEMPT_EDIT_SUBMISSION_MANDATORY_FIELDS,
          new ShipmentAttemptEditSubmissionMandatoryFields(
            shipmentId,
            tQueryRunner,
            this.request?.user,
            changedMandatoryFields
          )
        );
        throw new BadRequestException(
          `Cannot edit the following fields after shipment entry is submitted: ${NON_EDITABLE_FIELDS_POST_SUBMISSION.join(",")}`
        );
      }

      shipment.lastEditedBy = this.request?.user || null;
      const toBeUpdatedContainers: Array<Container> = [];
      const toBeRemovedContainers: Array<Container> = [];
      for (const [key, value] of Object.entries(editShipmentDto)) {
        if (value === undefined) continue;
        const propertyName = key.replace(/Id$/, "");
        switch (propertyName) {
          case "containers":
            const containerDtos = Array.isArray(value) ? value : [];

            // Create or update containers
            for (const dto of containerDtos) {
              const existingContainer = (shipment?.containers || []).find(
                (c) => c.containerNumber === dto.containerNumber
              );
              const toBeSavedContainer = new Container();
              if (existingContainer) toBeSavedContainer.id = existingContainer.id;
              else {
                toBeSavedContainer.shipment = shipment;
                toBeSavedContainer.createdBy = this.request?.user || null;
              }
              for (const [key, value] of Object.entries({ ...dto, lastEditedBy: this.request?.user || null }))
                if (value !== undefined) toBeSavedContainer[key] = value;
              toBeUpdatedContainers.push(toBeSavedContainer);
            }

            // Remove containers
            toBeRemovedContainers.push(
              ...(shipment?.containers || []).filter(
                (c) => !containerDtos.some((dto) => dto.containerNumber === c.containerNumber)
              )
            );
            break;
          case "portOfDischarge":
          case "portOfLoading":
          case "placeOfDelivery":
            await this.validateAndAssignLocation(value, propertyName, shipment, tQueryRunner);
            break;
          case "importer":
            await this.validateAndAssignImporter(value, propertyName, shipment, tQueryRunner);
            break;
          case "carrier":
          case "manufacturer":
          case "shipper":
          case "consignee":
          case "forwarder":
          case "trucker":
          case "pickupLocation":
            await this.validateAndAssignTradePartner(value, propertyName, shipment, tQueryRunner);
            break;
          default:
            if (!(SHIPMENT_REQUIRED_KEYS.includes(propertyName) && [null, undefined].includes(value)))
              shipment[propertyName] = value;
            break;
        }
      }

      for (const field of ["placeOfDelivery", "portOfDischarge"]) {
        if (
          shipment[field] &&
          !(await this.isCustomsCountrySameAsLocationCountry(
            shipment.customsCountry,
            shipment[field]?.id,
            tQueryRunner
          ))
        ) {
          throw new BadRequestException(`Customs country must be the same as ${field}'s country`);
        }
      }

      if (toBeUpdatedContainers.length > 0) {
        const savedContainers = await tQueryRunner.manager.save(toBeUpdatedContainers);
        this.logger.debug(
          `Created or updated containers: ${JSON.stringify(savedContainers.map((c) => c.id))}`
        );
        shipment.containers = savedContainers;
      }
      if (toBeRemovedContainers.length > 0) {
        const deleteResult = await tQueryRunner.manager.delete(Container, {
          id: In(toBeRemovedContainers.map((c) => c.id))
        });
        this.logger.debug(`Deleted containers count: ${deleteResult.affected}`);
        shipment.containers = shipment.containers.filter(
          (c) => !toBeRemovedContainers.find((rc) => rc.id === c.id)
        );
      }

      await tQueryRunner.manager.save(shipment);
      if (
        shipment.containers.length > 0 ||
        toBeUpdatedContainers.length > 0 ||
        toBeRemovedContainers.length > 0
      )
        await this.recalculateShipmentDatesAndStatus([shipment], tQueryRunner);

      this.eventEmitter.emit(
        ShipmentEvent.SHIPMENT_EDITED,
        new ShipmentEditedEventDto(shipmentId, tQueryRunner, this.request?.user, editShipmentDto)
      );
      if (
        this.complianceValidationService.isShipmentEntryUploaded(shipment) &&
        changedMandatoryFields.length > 0
      )
        this.eventEmitter.emit(
          ShipmentEvent.SHIPMENT_ATTEMPT_EDIT_SUBMISSION_MANDATORY_FIELDS,
          new ShipmentAttemptEditSubmissionMandatoryFields(
            shipment.id,
            tQueryRunner,
            this.request?.user,
            changedMandatoryFields
          )
        );

      if (!queryRunner) await tQueryRunner.commitTransaction();

      return await this.getShipmentById(shipmentId, queryRunner);
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async deleteShipment(shipmentId: number, queryRunner?: QueryRunner) {
    const shipment = await this.getShipmentById(shipmentId, queryRunner);

    if (!shipment) throw new NotFoundException("Shipment not found");

    const result = await (
      queryRunner ? queryRunner.manager.getRepository(Shipment) : this.shipmentRepository
    ).delete({
      id: shipmentId
    });

    // FIXME: this is a temporary solution to handle the case where the shipment is being used, we might need to switch to soft delete in the future
    if (result.affected === 0)
      throw new BadRequestException("Shipment cannot be deleted, please try again later");

    this.eventEmitter.emit(
      ShipmentEvent.SHIPMENT_DELETED,
      new ShipmentDeletedEventDto(shipmentId, queryRunner, this.request?.user)
    );

    return;
  }

  // #region Validate Shipment Compliance
  /**
   * Validates a shipment's compliance status
   *
   * This is used by api to get the compliance details of a shipment
   *
   * @param shipmentId The ID of the shipment to validate
   * @returns The compliance validation DTO for the shipment
   */
  async validateShipmentCompliance(shipmentId: number): Promise<ValidateShipmentComplianceResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipment = await this.getShipmentById(shipmentId, queryRunner);

      if (!shipment) {
        throw new NotFoundException("Shipment not found");
      }

      if (!shipment.requiresReupload && this.complianceValidationService.isShipmentEntryUploaded(shipment)) {
        throw new BadRequestException("Shipment entry is already uploaded");
      }

      const enrichedShipment = await this.enricher.enrich(shipment);

      const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances(
        [enrichedShipment],
        queryRunner
      );
      const shouldSkipFilingsValidation = this.complianceValidationService.isDemoShipment(enrichedShipment);

      return this.complianceValidationService.validateShipmentCompliances(
        shipmentCompliances,
        shouldSkipFilingsValidation
      )[0];
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  // #endregion Validate Shipment Compliance

  // #region Get Shipment Duty Summary
  async getShipmentDutySummary(shipmentId: number): Promise<GetShipmentDutySummaryResponseDto> {
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) throw new NotFoundException("Shipment not found");
    if (!shipment.transactionNumber && !shipment.customsFileNumber)
      throw new BadRequestException("Shipment has no transaction number and file number");
    const candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
      shipment.transactionNumber,
      shipment.customsFileNumber
    );

    let candataShipment: CandataShipmentDto = null;
    try {
      candataShipment = await this.candataService.getCandataShipment(
        candataShipmentIdentifier,
        shipment.organization?.customsBroker
      );
    } catch (error) {
      this.logger.error(`Error while getting Candata shipment: ${error.message}`);
      candataShipment = null;
    }
    if (!candataShipment) throw new NotFoundException("Shipment not found in Candata");

    const commercialInvoiceLineList = await this.commercialInvoiceLineRepository.find({
      where: { commercialInvoice: { shipment: { id: shipment.id } } }
    });

    const dutySummaryLines: Array<DutySummaryLineDto> = [];
    let sequence = 1;
    for (const cci of candataShipment?.ccis || []) {
      for (const cciLine of cci?.invoiceSummary?.details || []) {
        const dbCiLine = commercialInvoiceLineList.find((line) => line.candataId === cciLine.id);
        dutySummaryLines.push({
          sequence: sequence++,
          description: cciLine.description,
          hsCode: cciLine.classification,
          quantity: cciLine.quantity,
          quantityUOM: cciLine.unitOfMeasure,
          vfd: cciLine.vfdCode || null,
          simaCode: cciLine.sima || null,
          currency: cci.currency,
          valueForDuty: dbCiLine?.valueForDuty || 0,
          antiDumping: dbCiLine?.antiDumping || 0,
          countervailing: dbCiLine?.countervailing || 0,
          customsDuties: dbCiLine?.customsDuties || 0,
          exciseDuties: dbCiLine?.exciseDuties || 0,
          exciseTax: dbCiLine?.exciseTax || 0,
          gst: dbCiLine?.gst || 0,
          pstHst: dbCiLine?.pstHst || 0,
          provincialAlcoholTax: dbCiLine?.provincialAlcoholTax || 0,
          provincialCannabisExciseDuty: dbCiLine?.provincialCannabisExciseDuty || 0,
          provincialTobaccoTax: dbCiLine?.provincialTobaccoTax || 0,
          safeguard: dbCiLine?.safeguard || 0,
          surtax: dbCiLine?.surtax || 0,
          totalDutiesAndTaxes: dbCiLine?.totalDutiesAndTaxes || 0
        });
      }
    }

    return {
      dutySummaryLines: dutySummaryLines.sort((a, b) => a.sequence - b.sequence)
    };
  }
  // #endregion Get Shipment Duty Summary

  // #region Get Shipment Customs Activities
  async getShipmentCustomsActivities(shipmentId: number): Promise<GetShipmentCustomsActivitiesResponseDto> {
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) throw new NotFoundException("Shipment not found");
    if (!shipment.transactionNumber) throw new BadRequestException("Shipment has no transaction number");

    let cbsaActivities: Array<CandataRNSResponseDto> | null = null;
    try {
      cbsaActivities = await this.candataService.findRnsResponseByTransactionNumbers(
        [shipment.transactionNumber],
        shipment.organization?.customsBroker
      );
    } catch (error) {
      this.logger.error(`Error while getting Candata shipment customs activities: ${error.message}`);
      cbsaActivities = null;
    }
    if (!Array.isArray(cbsaActivities))
      throw new BadRequestException("Error while getting customs activities from Candata");

    const processingIndicatorMapping = Object.entries(RNSProcessingIndicator).reduce(
      (map, [msg, indicator]) => {
        map[indicator] = msg
          .split("_")
          .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
          .join(" ");
        return map;
      },
      {} as Record<number, string>
    );

    return {
      customsActivities: cbsaActivities.map((ca) => ({
        responseDate: ca.responseDate,
        transactionNumber: ca.transactionNumber,
        event: processingIndicatorMapping[ca.processingIndicator],
        portCode: ca.port || null,
        subLocationCode: ca.sublocation || null
      }))
    };
  }
  // #endregion Get Shipment Customs Activities

  /**
   * Retrieves a detailed compliance validation report for a specific shipment.
   *
   * Currently this is used by agent-tools service
   *
   * @param shipmentId The ID of the shipment to validate
   * @returns The compliance validation DTO for the shipment
   */
  async getShipmentComplianceDetails(shipmentId: number): Promise<any> {
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) throw new BadRequestException(`Shipment ${shipmentId} not found.`);
    const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([shipment]);
    if (!shipmentCompliances.length) {
      throw new BadRequestException(`No compliance data found for shipment ${shipmentId}.`);
    }
    const skipFilingsValidation = this.complianceValidationService.isDemoShipment(shipment);
    const validationResults = this.complianceValidationService.validateShipmentCompliances(
      [shipmentCompliances[0]],
      skipFilingsValidation
    );
    if (!validationResults.length) {
      throw new BadRequestException(`No validation results for shipment ${shipmentId}.`);
    }
    return validationResults[0];
  }

  async getEnrichmentResult(shipmentId: number) {
    const shipment = await this.getShipmentById(shipmentId);
    if (!shipment) throw new NotFoundException("Shipment not found");
    return {
      fields: await this.enricher.getFallbacks(shipment)
    };
  }
}
