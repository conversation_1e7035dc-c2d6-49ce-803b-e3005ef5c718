import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { DocumentStatus, FileStatus } from "nest-modules";
import { ShipmentContext } from "../../agent-context/types/shipment-context.types";
import { ValidatedIntent } from "../types/response-fragment.types";

/**
 * Service for processing documents and attachments for intent handlers.
 * Provides utilities for validating, processing, and fetching document data.
 */
@Injectable()
export class DocumentProcessorService {
  private readonly logger = new Logger(DocumentProcessorService.name);

  constructor(@InjectDataSource() private readonly dataSource: DataSource) {}

  /**
   * Validates CAD attachment structure and content.
   */
  validateCADAttachment(attachment: any): attachment is {
    fileName: string;
    mimeType: string;
    b64Data: string;
  } {
    return (
      attachment &&
      typeof attachment === "object" &&
      typeof attachment.fileName === "string" &&
      attachment.fileName.length > 0 &&
      typeof attachment.mimeType === "string" &&
      attachment.mimeType.length > 0 &&
      typeof attachment.b64Data === "string" &&
      attachment.b64Data.length > 0
    );
  }

  /**
   * Processes document attachments from validated intent and formats them for template rendering.
   */
  processDocumentAttachments(validatedIntent: ValidatedIntent): any[] {
    if (!validatedIntent.attachments || validatedIntent.attachments.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${validatedIntent.attachments.length} document attachments`);

    return validatedIntent.attachments.map((attachment, index) => {
      // Provide better fallback names based on attachment properties
      let filename = attachment.filename;

      if (!filename || filename.trim() === "") {
        // Try to get filename from documentType or extractedData
        if (attachment.documentType) {
          filename = attachment.documentType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        } else {
          filename = `Document ${index + 1}`;
        }
      }

      this.logger.debug(
        `Processing attachment ${index + 1}: ` +
          `originalFilename="${attachment.filename}", ` +
          `documentType="${attachment.documentType}", ` +
          `finalName="${filename}", ` +
          `id="${attachment.id}"`
      );

      return {
        filename: filename, // Changed from 'name' to 'filename' to match template
        contentType: this.formatDocumentTypeName(attachment.documentType || "Document"), // Format document type for display
        status: attachment.status ? this.formatStatusForDisplay(attachment.status) : "processed", // Use actual status or fallback
        claroUrl: attachment.id
          ? `https://portal.clarocustoms.com/document/${attachment.id}`
          : `https://portal.clarocustoms.com/documents`
      };
    });
  }

  /**
   * Fetches processed documents from database when attachments are not available in the intent.
   * This queries the actual database for documents and files associated with the shipment.
   */
  async fetchProcessedDocumentsFromDatabase(context: ShipmentContext): Promise<any[]> {
    const processedDocuments: any[] = [];

    try {
      const shipmentId = context.shipment.id;
      const organizationId = context.organization.id;

      this.logger.debug(`📄 FETCHING DOCUMENTS: Querying database for shipment ${shipmentId} documents...`);

      // Fetch documents from Document table
      const documentRepository = this.dataSource.getRepository("Document");
      const documents = await documentRepository.find({
        where: {
          shipmentId,
          organizationId
        },
        relations: ["documentType"]
      });

      // Fetch files from File table
      const fileRepository = this.dataSource.getRepository("File");
      const files = await fileRepository.find({
        where: {
          shipmentId,
          organizationId
        }
      });

      this.logger.debug(
        `📄 FETCHED FROM DB: ${documents.length} documents, ${files.length} files for shipment ${shipmentId}`
      );

      // Process documents
      documents.forEach((doc, index) => {
        const documentName =
          doc.name && doc.name.trim() !== "" ? doc.name : doc.documentType?.name || `Document ${index + 1}`;

        processedDocuments.push({
          filename: documentName, // Changed from 'name' to 'filename' to match template
          contentType: this.formatDocumentTypeName(doc.name || doc.documentType?.name || "Document"), // Use Document.name field for document type
          status: this.formatStatusForDisplay(doc.displayStatus || doc.status), // Use displayStatus (considers shipment mismatch) or fallback to status
          claroUrl: `https://portal.clarocustoms.com/shipment/${shipmentId}/document/${doc.id}`
        });
      });

      // Process files
      files.forEach((file, index) => {
        const fileName = file.name && file.name.trim() !== "" ? file.name : `File ${index + 1}`;

        processedDocuments.push({
          filename: fileName, // Changed from 'name' to 'filename' to match template
          contentType: "File", // Files don't have document types from aggregation
          status: this.formatStatusForDisplay(file.status), // Use actual file status
          claroUrl: `https://portal.clarocustoms.com/shipment/${shipmentId}/file/${file.id}`
        });
      });

      // No fallback to commercial invoices - if no actual documents/files were processed, return empty array
      // This is more honest - commercial invoices are business data, not "processed documents"
    } catch (error) {
      this.logger.error(
        `Failed to fetch documents from database for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
    }

    return processedDocuments;
  }

  /**
   * Formats document type name for display (converts enum values to readable names).
   * Example: "COMMERCIAL_INVOICE" -> "Commercial Invoice"
   */
  formatDocumentTypeName(documentType: string): string {
    return documentType.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
  }

  /**
   * Formats status values for display (converts enum values to lowercase, no underscores).
   * Example: "EXTRACTION_FAILED" -> "extraction failed"
   */
  private formatStatusForDisplay(status: DocumentStatus | FileStatus): string {
    return status.replace(/_/g, " ").toLowerCase();
  }
}
