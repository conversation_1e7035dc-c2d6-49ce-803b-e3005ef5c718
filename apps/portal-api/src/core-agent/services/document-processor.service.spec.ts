import { Test, TestingModule } from "@nestjs/testing";
import { getDataSourceToken } from "@nestjs/typeorm";
import { DocumentStatus, FileStatus } from "nest-modules";
import { DocumentProcessorService } from "./document-processor.service";

describe("DocumentProcessorService", () => {
  let service: DocumentProcessorService;
  let mockDataSource: any;

  beforeEach(async () => {
    mockDataSource = {
      getRepository: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentProcessorService,
        {
          provide: getDataSourceToken(),
          useValue: mockDataSource
        }
      ]
    }).compile();

    service = module.get<DocumentProcessorService>(DocumentProcessorService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("formatStatusForDisplay", () => {
    it("should format DocumentStatus enum values correctly", () => {
      // Access the private method for testing
      const formatStatus = (service as any).formatStatusForDisplay.bind(service);

      expect(formatStatus(DocumentStatus.PENDING)).toBe("pending");
      expect(formatStatus(DocumentStatus.EXTRACTING)).toBe("extracting");
      expect(formatStatus(DocumentStatus.EXTRACTED)).toBe("extracted");
      expect(formatStatus(DocumentStatus.VALIDATING)).toBe("validating");
      expect(formatStatus(DocumentStatus.AGGREGATED)).toBe("aggregated");
      expect(formatStatus(DocumentStatus.EXTRACTION_FAILED)).toBe("extraction failed");
      expect(formatStatus(DocumentStatus.AGGREGATION_FAILED)).toBe("aggregation failed");
      expect(formatStatus(DocumentStatus.SHIPMENT_MISMATCH)).toBe("shipment mismatch");
    });

    it("should format FileStatus enum values correctly", () => {
      const formatStatus = (service as any).formatStatusForDisplay.bind(service);

      expect(formatStatus(FileStatus.PENDING)).toBe("pending");
      expect(formatStatus(FileStatus.PARSING)).toBe("parsing");
      expect(formatStatus(FileStatus.SPLITTING)).toBe("splitting");
      expect(formatStatus(FileStatus.PROCESSED)).toBe("processed");
      expect(formatStatus(FileStatus.NO_DOCUMENT_DETECTED)).toBe("no document detected");
      expect(formatStatus(FileStatus.FILE_CORRUPTED)).toBe("file corrupted");
      expect(formatStatus(FileStatus.PARSE_FAILED)).toBe("parse failed");
      expect(formatStatus(FileStatus.SPLIT_FAILED)).toBe("split failed");
      expect(formatStatus(FileStatus.ERROR)).toBe("error");
    });
  });

  describe("processDocumentAttachments", () => {
    it("should use actual status when available", () => {
      const validatedIntent = {
        attachments: [
          {
            id: "doc-1",
            filename: "test.pdf",
            documentType: "COMMERCIAL_INVOICE",
            status: DocumentStatus.EXTRACTED
          },
          {
            id: "doc-2",
            filename: "test2.pdf",
            documentType: "BILL_OF_LADING",
            status: DocumentStatus.EXTRACTION_FAILED
          }
        ]
      };

      const result = service.processDocumentAttachments(validatedIntent);

      expect(result).toHaveLength(2);
      expect(result[0].status).toBe("extracted");
      expect(result[1].status).toBe("extraction failed");
    });

    it("should fallback to 'processed' when status is not available", () => {
      const validatedIntent = {
        attachments: [
          {
            id: "doc-1",
            filename: "test.pdf",
            documentType: "COMMERCIAL_INVOICE"
            // no status field
          }
        ]
      };

      const result = service.processDocumentAttachments(validatedIntent);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe("processed");
    });
  });

  describe("fetchProcessedDocumentsFromDatabase", () => {
    it("should use displayStatus over status for documents", async () => {
      const mockDocuments = [
        {
          id: 1,
          name: "Test Document",
          status: DocumentStatus.EXTRACTED,
          displayStatus: DocumentStatus.SHIPMENT_MISMATCH,
          documentType: { name: "Commercial Invoice" }
        }
      ];

      const mockFiles = [];

      const mockDocumentRepository = {
        find: jest.fn().mockResolvedValue(mockDocuments)
      };
      const mockFileRepository = {
        find: jest.fn().mockResolvedValue(mockFiles)
      };

      mockDataSource.getRepository.mockImplementation((entityName: string) => {
        if (entityName === "Document") return mockDocumentRepository;
        if (entityName === "File") return mockFileRepository;
        return null;
      });

      const context = {
        shipment: { id: 123 },
        organization: { id: 456 }
      };

      const result = await service.fetchProcessedDocumentsFromDatabase(context as any);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe("shipment mismatch"); // displayStatus used
    });

    it("should use file status for files", async () => {
      const mockDocuments = [];
      const mockFiles = [
        {
          id: 1,
          name: "test-file.pdf",
          status: FileStatus.PROCESSED
        },
        {
          id: 2,
          name: "failed-file.pdf",
          status: FileStatus.PARSE_FAILED
        }
      ];

      const mockDocumentRepository = {
        find: jest.fn().mockResolvedValue(mockDocuments)
      };
      const mockFileRepository = {
        find: jest.fn().mockResolvedValue(mockFiles)
      };

      mockDataSource.getRepository.mockImplementation((entityName: string) => {
        if (entityName === "Document") return mockDocumentRepository;
        if (entityName === "File") return mockFileRepository;
        return null;
      });

      const context = {
        shipment: { id: 123 },
        organization: { id: 456 }
      };

      const result = await service.fetchProcessedDocumentsFromDatabase(context as any);

      expect(result).toHaveLength(2);
      expect(result[0].status).toBe("processed");
      expect(result[1].status).toBe("parse failed");
    });
  });
});
