#!/usr/bin/env node

/**
 * Document Processor Status Testing Script
 *
 * This script tests the DocumentProcessorService to verify that status values
 * are correctly formatted and use actual enum values instead of hard-coded "Processed".
 *
 * Usage:
 *   node src/core-agent/testing/test-document-processor-status.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --shipment=ID              Specific shipment ID to test with
 *   --verbose                  Show detailed logging
 *
 * Examples:
 *   # Test with auto-selected shipment
 *   node src/core-agent/testing/test-document-processor-status.js --verbose
 *
 *   # Test with specific shipment
 *   node src/core-agent/testing/test-document-processor-status.js --shipment=123 --verbose
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services
const { ShipmentContextService } = require("../../../dist/agent-context");
const { DocumentProcessorService } = require("../../../dist/core-agent/services/document-processor.service");

// Import enums for testing
const { DocumentStatus, FileStatus } = require("nest-modules");

// Test data for status formatting
const TEST_DOCUMENT_STATUSES = [
  DocumentStatus.PENDING,
  DocumentStatus.EXTRACTING,
  DocumentStatus.EXTRACTED,
  DocumentStatus.VALIDATING,
  DocumentStatus.AGGREGATED,
  DocumentStatus.EXTRACTION_FAILED,
  DocumentStatus.AGGREGATION_FAILED,
  DocumentStatus.SHIPMENT_MISMATCH
];

const TEST_FILE_STATUSES = [
  FileStatus.PENDING,
  FileStatus.PARSING,
  FileStatus.SPLITTING,
  FileStatus.PROCESSED,
  FileStatus.NO_DOCUMENT_DETECTED,
  FileStatus.FILE_CORRUPTED,
  FileStatus.PARSE_FAILED,
  FileStatus.SPLIT_FAILED,
  FileStatus.ERROR
];

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = {
    organizationId: "3", // Default to demo org
    shipmentId: null,
    verbose: false
  };

  args.forEach((arg) => {
    if (arg.startsWith("--org=")) {
      config.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--shipment=")) {
      config.shipmentId = arg.split("=")[1];
    } else if (arg === "--verbose") {
      config.verbose = true;
    }
  });

  return config;
}

// Test status formatting functionality
function testStatusFormatting(documentProcessorService) {
  console.log("\n🧪 Testing Status Formatting");
  console.log("============================");

  // Test private method access (for testing purposes)
  const formatStatusForDisplay = documentProcessorService.formatStatusForDisplay?.bind(documentProcessorService);
  
  if (!formatStatusForDisplay) {
    console.log("❌ formatStatusForDisplay method not accessible - this is expected for private methods");
    console.log("   Status formatting will be tested through public methods instead");
    return;
  }

  console.log("\n📄 Document Status Formatting:");
  TEST_DOCUMENT_STATUSES.forEach(status => {
    const formatted = formatStatusForDisplay(status);
    console.log(`  ${status.padEnd(20)} → "${formatted}"`);
  });

  console.log("\n📁 File Status Formatting:");
  TEST_FILE_STATUSES.forEach(status => {
    const formatted = formatStatusForDisplay(status);
    console.log(`  ${status.padEnd(25)} → "${formatted}"`);
  });
}

// Test processDocumentAttachments with mock data
function testProcessDocumentAttachments(documentProcessorService) {
  console.log("\n🧪 Testing processDocumentAttachments");
  console.log("====================================");

  const testCases = [
    {
      name: "With status values",
      validatedIntent: {
        attachments: [
          {
            id: "doc-1",
            filename: "commercial-invoice.pdf",
            documentType: "COMMERCIAL_INVOICE",
            status: DocumentStatus.EXTRACTED
          },
          {
            id: "doc-2",
            filename: "bill-of-lading.pdf",
            documentType: "BILL_OF_LADING",
            status: DocumentStatus.EXTRACTION_FAILED
          }
        ]
      }
    },
    {
      name: "Without status values (fallback test)",
      validatedIntent: {
        attachments: [
          {
            id: "doc-3",
            filename: "packing-list.pdf",
            documentType: "PACKING_LIST"
            // No status field - should fallback to "processed"
          }
        ]
      }
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📋 Test Case: ${testCase.name}`);
    try {
      const result = documentProcessorService.processDocumentAttachments(testCase.validatedIntent);
      result.forEach((doc, index) => {
        console.log(`  Document ${index + 1}:`);
        console.log(`    filename: "${doc.filename}"`);
        console.log(`    contentType: "${doc.contentType}"`);
        console.log(`    status: "${doc.status}" ${doc.status !== "Processed" ? "✅" : "❌ Still hard-coded!"}`);
        console.log(`    claroUrl: "${doc.claroUrl}"`);
      });
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  });
}

// Find a test shipment
async function findTestShipment(dataSource, organizationId) {
  const shipmentRepository = dataSource.getRepository("Shipment");
  
  const shipments = await shipmentRepository.find({
    where: { organizationId: parseInt(organizationId) },
    relations: ["documents", "files"],
    take: 5
  });

  // Prefer shipments with documents/files
  const shipmentWithDocs = shipments.find(s => s.documents?.length > 0 || s.files?.length > 0);
  return shipmentWithDocs || shipments[0];
}

// Test fetchProcessedDocumentsFromDatabase with real data
async function testFetchProcessedDocumentsFromDatabase(documentProcessorService, context, config) {
  console.log("\n🧪 Testing fetchProcessedDocumentsFromDatabase");
  console.log("==============================================");

  try {
    const result = await documentProcessorService.fetchProcessedDocumentsFromDatabase(context);
    
    console.log(`\n📊 Found ${result.length} processed documents/files:`);
    
    if (result.length === 0) {
      console.log("  No documents or files found for this shipment");
      return;
    }

    result.forEach((item, index) => {
      console.log(`\n  Item ${index + 1}:`);
      console.log(`    filename: "${item.filename}"`);
      console.log(`    contentType: "${item.contentType}"`);
      console.log(`    status: "${item.status}" ${item.status !== "Processed" ? "✅" : "❌ Still hard-coded!"}`);
      console.log(`    claroUrl: "${item.claroUrl}"`);
    });

    // Check for hard-coded "Processed" values
    const hardCodedCount = result.filter(item => item.status === "Processed").length;
    if (hardCodedCount > 0) {
      console.log(`\n❌ Found ${hardCodedCount} items still using hard-coded "Processed" status!`);
    } else {
      console.log(`\n✅ All ${result.length} items are using actual status values!`);
    }

  } catch (error) {
    console.log(`❌ Error testing fetchProcessedDocumentsFromDatabase: ${error.message}`);
    if (config.verbose) {
      console.log(error.stack);
    }
  }
}

// Main test function
async function runTests(config, app, dataSource) {
  try {
    console.log(`🚀 Testing Document Processor Status Functionality`);
    console.log(`📋 Organization: ${config.organizationId}`);
    
    // 1. Get organization
    const organizationRepository = dataSource.getRepository("Organization");
    const organization = await organizationRepository.findOne({
      where: { id: parseInt(config.organizationId) }
    });

    if (!organization) {
      throw new Error(`Organization ${config.organizationId} not found`);
    }

    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const documentProcessorService = await app.resolve(DocumentProcessorService, contextId);

    console.log(`✅ Resolved DocumentProcessorService`);

    // 4. Test status formatting
    testStatusFormatting(documentProcessorService);

    // 5. Test processDocumentAttachments
    testProcessDocumentAttachments(documentProcessorService);

    // 6. Find test shipment
    let shipment;
    if (config.shipmentId) {
      const shipmentRepository = dataSource.getRepository("Shipment");
      shipment = await shipmentRepository.findOne({
        where: { id: parseInt(config.shipmentId), organizationId: parseInt(config.organizationId) },
        relations: ["documents", "files"]
      });
      if (!shipment) {
        throw new Error(`Shipment ${config.shipmentId} not found in organization ${config.organizationId}`);
      }
    } else {
      shipment = await findTestShipment(dataSource, config.organizationId);
      if (!shipment) {
        throw new Error(`No shipments found in organization ${config.organizationId}`);
      }
    }

    console.log(`\n✅ Using shipment ${shipment.id} (${shipment.hblNumber || 'No HBL'})`);
    console.log(`   Documents: ${shipment.documents?.length || 0}, Files: ${shipment.files?.length || 0}`);

    // 7. Build shipment context
    const context = await shipmentContextService.buildContext(shipment.id, parseInt(config.organizationId));

    // 8. Test fetchProcessedDocumentsFromDatabase
    await testFetchProcessedDocumentsFromDatabase(documentProcessorService, context, config);

    console.log(`\n🎉 Document Processor Status Testing completed!`);

  } catch (error) {
    console.error(`💥 Test failed: ${error.message}`);
    if (config.verbose) {
      console.error(error.stack);
    }
    throw error;
  }
}

// Bootstrap and run tests
async function bootstrap() {
  const config = parseArgs();
  let app;

  try {
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    await runTests(config, app, dataSource);

  } catch (error) {
    console.error(`💥 Bootstrap failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Show usage if help requested
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log("Document Processor Status Testing Script");
  console.log("=======================================");
  console.log("");
  console.log("Tests the DocumentProcessorService status formatting functionality");
  console.log("to ensure actual enum values are used instead of hard-coded 'Processed'.");
  console.log("");
  console.log("Options:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --shipment=ID              Specific shipment ID to test with");
  console.log("  --verbose                  Show detailed logging");
  console.log("  --help, -h                 Show this help message");
  console.log("");
  console.log("Examples:");
  console.log("  node src/core-agent/testing/test-document-processor-status.js --verbose");
  console.log("  node src/core-agent/testing/test-document-processor-status.js --shipment=123");
  process.exit(0);
}

// Run the bootstrap
bootstrap();
