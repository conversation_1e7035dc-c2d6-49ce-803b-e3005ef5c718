import {
  ShipmentColumn,
  CommercialInvoiceColumn,
  TradePartnerColumn,
  // Import needed enums for completeness
  VolumeUOM,
  QuantityUOM,
  CustomsStatus,
  ShipmentStatus,
  TrackingStatus,
  Currency,
  WeightUOM,
  PackageUOM,
  PartnerType
} from "nest-modules";

export const MISSING_SHIPMENT_FIELD_DESCRIPTIONS: Record<ShipmentColumn, string> = {
  [ShipmentColumn.cargoControlNumber]: "Missing Cargo Control Number (CCN)",
  [ShipmentColumn.portCode]: "Missing Port Code",
  [ShipmentColumn.subLocation]: "Missing Sublocation Code",
  [ShipmentColumn.modeOfTransport]: "Missing Mode of Transport",
  [ShipmentColumn.weight]: "Missing Total Shipment Weight",
  [ShipmentColumn.weightUOM]: "Missing Unit for Total Shipment Weight",
  [ShipmentColumn.portOfLoadingId]: "Missing Port of Loading",
  [ShipmentColumn.importerId]: "Missing Importer of Record",
  [ShipmentColumn.etaDestination]: "Missing Estimated Time of Arrival (ETA) at Destination",
  [ShipmentColumn.etd]: "Missing Estimated Time of Departure (ETD)",
  [ShipmentColumn.status]: "Missing Shipment Status",
  [ShipmentColumn.etaPort]: "Missing Estimated Time of Arrival (ETA) at Port",
  [ShipmentColumn.mblNumber]: "Missing Master Bill of Lading (MBL) Number",
  [ShipmentColumn.hblNumber]: "Missing House Bill of Lading (HBL) Number",
  [ShipmentColumn.carrierCode]: "Missing Carrier Code",
  [ShipmentColumn.volume]: "Missing Shipment Volume",
  [ShipmentColumn.volumeUOM]: "Missing Unit for Shipment Volume",
  [ShipmentColumn.quantity]: "Missing Shipment Quantity",
  [ShipmentColumn.quantityUOM]: "Missing Unit for Shipment Quantity",
  [ShipmentColumn.pickupLfd]: "Missing Pickup Last Free Day (LFD)",
  [ShipmentColumn.pickupDate]: "Missing Pickup Date",
  [ShipmentColumn.pickupNumber]: "Missing Pickup Number",
  [ShipmentColumn.returnLfd]: "Missing Return Last Free Day (LFD)",
  [ShipmentColumn.returnDate]: "Missing Return Date",
  [ShipmentColumn.vessel]: "Missing Vessel Name",
  [ShipmentColumn.voyageNumber]: "Missing Voyage Number",
  [ShipmentColumn.customsStatus]: "Missing Customs Status",
  [ShipmentColumn.transactionNumber]: "Missing Customs Transaction Number",
  [ShipmentColumn.customsFileNumber]: "Missing Customs File Number",
  [ShipmentColumn.portOfExit]: "Missing Port of Exit",
  [ShipmentColumn.portOfDischargeId]: "Missing Port of Discharge",
  [ShipmentColumn.placeOfDeliveryId]: "Missing Place of Delivery",
  [ShipmentColumn.carrierId]: "Missing Carrier",
  [ShipmentColumn.manufacturerId]: "Missing Manufacturer",
  [ShipmentColumn.shipperId]: "Missing Shipper",
  [ShipmentColumn.consigneeId]: "Missing Consignee",
  [ShipmentColumn.forwarderId]: "Missing Forwarder",
  [ShipmentColumn.truckerId]: "Missing Trucker",
  [ShipmentColumn.pickupLocationId]: "Missing Pickup Location",
  [ShipmentColumn.id]: "Shipment ID",
  [ShipmentColumn.trackingStatus]: "Tracking Status",
  [ShipmentColumn.etaPortString]: "ETA Port (String)",
  [ShipmentColumn.etaDestinationString]: "ETA Destination (String)",
  [ShipmentColumn.containerNumber]: "Container Number",
  [ShipmentColumn.containerType]: "Container Type",
  [ShipmentColumn.pickupLfdString]: "Pickup LFD (String)",
  [ShipmentColumn.pickupDateString]: "Pickup Date (String)",
  [ShipmentColumn.returnLfdString]: "Return LFD (String)",
  [ShipmentColumn.returnDateString]: "Return Date (String)",
  [ShipmentColumn.createDate]: "Creation Date",
  [ShipmentColumn.lastEditDate]: "Last Edit Date",
  [ShipmentColumn.organizationId]: "Organization ID",
  [ShipmentColumn.createdById]: "Creator User ID",
  [ShipmentColumn.lastEditedById]: "Editor User ID",
  [ShipmentColumn.requiresReupload]: "Missing Requires Reupload Flag",
  [ShipmentColumn.customsCountry]: "Missing Customs Country"
};

export const MISSING_COMMERCIAL_INVOICE_FIELD_DESCRIPTIONS: Record<CommercialInvoiceColumn, string> = {
  [CommercialInvoiceColumn.invoiceNumber]: "Missing Invoice Number",
  [CommercialInvoiceColumn.currency]: "Missing Invoice Currency",
  [CommercialInvoiceColumn.invoiceDate]: "Missing Invoice Date",
  [CommercialInvoiceColumn.poNumber]: "Missing Purchase Order (PO) Number",
  [CommercialInvoiceColumn.grossWeight]: "Missing Invoice Gross Weight",
  [CommercialInvoiceColumn.weightUOM]: "Missing Unit for Invoice Weight",
  [CommercialInvoiceColumn.numberOfPackages]: "Missing Number of Packages",
  [CommercialInvoiceColumn.packageUOM]: "Missing Unit for Packages",
  [CommercialInvoiceColumn.countryOfExportId]: "Missing Country of Export",
  [CommercialInvoiceColumn.exporterId]: "Missing Exporter",
  [CommercialInvoiceColumn.vendorId]: "Missing Vendor/Seller",
  [CommercialInvoiceColumn.manufacturerId]: "Missing Manufacturer (Invoice Level)",
  [CommercialInvoiceColumn.shipToId]: "Missing Ship-To Party",
  [CommercialInvoiceColumn.purchaserId]: "Missing Purchaser",
  [CommercialInvoiceColumn.includedTransCost]: "Missing Included Transportation Cost",
  [CommercialInvoiceColumn.includedPackCost]: "Missing Included Packing Cost",
  [CommercialInvoiceColumn.includedMiscCost]: "Missing Included Miscellaneous Cost",
  [CommercialInvoiceColumn.excludedTransCost]: "Missing Excluded Transportation Cost",
  [CommercialInvoiceColumn.excludedPackCost]: "Missing Excluded Packing Cost",
  [CommercialInvoiceColumn.excludedMiscCost]: "Missing Excluded Miscellaneous Cost",
  [CommercialInvoiceColumn.valueIncludesDuty]: "Missing 'Value Includes Duty' Status",
  [CommercialInvoiceColumn.additionalInfo]: "Missing Additional Information",
  [CommercialInvoiceColumn.id]: "Invoice ID",
  [CommercialInvoiceColumn.createDate]: "Creation Date",
  [CommercialInvoiceColumn.lastEditDate]: "Last Edit Date",
  [CommercialInvoiceColumn.shipmentId]: "Shipment ID (Association)",
  [CommercialInvoiceColumn.organizationId]: "Organization ID (Association)",
  [CommercialInvoiceColumn.createdById]: "Creator User ID",
  [CommercialInvoiceColumn.lastEditedById]: "Editor User ID",
  [CommercialInvoiceColumn.candataId]: "Missing Candata ID",
  [CommercialInvoiceColumn.stateOfExportId]: "Missing State of Export"
};

export const MISSING_TRADE_PARTNER_FIELD_DESCRIPTIONS: Record<TradePartnerColumn, string> = {
  [TradePartnerColumn.name]: "Missing Partner Name",
  [TradePartnerColumn.partnerType]: "Missing Partner Type",
  [TradePartnerColumn.vendorCode]: "Missing Partner Vendor Code",
  [TradePartnerColumn.email]: "Missing Partner Email",
  [TradePartnerColumn.phoneNumber]: "Missing Partner Phone Number",
  [TradePartnerColumn.address]: "Missing Partner Street Address",
  [TradePartnerColumn.city]: "Missing Partner City",
  [TradePartnerColumn.state]: "Missing Partner State/Province",
  [TradePartnerColumn.postalCode]: "Missing Partner Postal/ZIP Code",
  [TradePartnerColumn.countryId]: "Missing Partner Country",
  [TradePartnerColumn.id]: "Partner ID",
  [TradePartnerColumn.createDate]: "Creation Date",
  [TradePartnerColumn.lastEditDate]: "Last Edit Date",
  [TradePartnerColumn.organizationId]: "Organization ID (Association)",
  [TradePartnerColumn.createdById]: "Creator User ID",
  [TradePartnerColumn.lastEditedById]: "Editor User ID",
  [TradePartnerColumn.eManifestCarrierCode]: "Missing eManifest Carrier Code"
};
