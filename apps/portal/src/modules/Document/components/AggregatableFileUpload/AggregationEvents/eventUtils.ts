import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { CheckCircleIcon, InfoIcon, XCircleIcon } from "lucide-react";

export type EventType =
  | "file-status-updated"
  | "document-created"
  | "document-extracted"
  | "document-aggregation-created"
  | "document-aggregation-success"
  | "document-aggregation-failed"
  | "all-documents-extracted"
  | "batch-shipment-created"
  | "batch-document-aggregated"
  | "batch.shipmentCreated"
  | "batch.shipmentCreationFailed"
  | "batch-checking-failed"
  | "batch-documents-validated"
  | "batch-documents-extracted";

export type EventStatus = "success" | "error" | "info";

export interface FormattedEvent {
  id: string;
  status: EventStatus;
  message: string;
  date: Date;
}

export const getEventDetails = (event: EventSourceMessage): FormattedEvent => {
  const data = JSON.parse(event.data);

  const formattedEvent: FormattedEvent = {
    id: event.id,
    status: "info",
    message: "Unknown event",
    date: new Date()
  };

  switch (event.event) {
    case "file-status-updated":
      formattedEvent.status = "info";
      formattedEvent.message = `${data.status} file ${data.fileId}.`;
      return formattedEvent;
    case "document-created":
      formattedEvent.status = "success";
      formattedEvent.message = `Found ${data.name} in file ${data.fileId}.`;
      return formattedEvent;
    case "document-extracted":
      formattedEvent.status = "success";
      formattedEvent.message = `Document ${data.documentId} extracted successfully.`;
      return formattedEvent;
    case "document-aggregation-created":
      formattedEvent.status = "success";
      formattedEvent.message = `Aggregation ${data.aggregationId} created for document ${data.documentId}.`;
      return formattedEvent;
    case "document-aggregation-success":
      formattedEvent.status = "success";
      formattedEvent.message = `Aggregation ${data.aggregationId} for document ${data.documentId} succeeded.`;
      return formattedEvent;
    case "document-aggregation-failed":
      formattedEvent.status = "error";
      formattedEvent.message = `Aggregation ${data.aggregationId} for document ${data.documentId} failed.`;
      return formattedEvent;
    case "all-documents-extracted":
      formattedEvent.status = "success";
      formattedEvent.message = `All documents extracted successfully.`;
      return formattedEvent;
    case "batch-shipment-created":
      formattedEvent.status = "success";
      formattedEvent.message = `Shipment ${data.shipmentId} created successfully.`;
      return formattedEvent;
    case "batch-document-aggregated":
      formattedEvent.status = "info";
      formattedEvent.message = `All documents aggregated`;
      return formattedEvent;
    case "batch-documents-validation-error":
      formattedEvent.status = "error";
      formattedEvent.message = data.error;
      return formattedEvent;
    case "batch.shipmentCreated":
      formattedEvent.status = "success";
      formattedEvent.message = `Shipment ${data.shipmentId} created successfully.`;
      return formattedEvent;
    case "batch.shipmentCreationFailed":
      formattedEvent.status = "error";
      formattedEvent.message = `Cannot create shipment because ${data.reason}.`;
      return formattedEvent;
    case "batch-checking-failed":
      formattedEvent.status = "error";
      formattedEvent.message = data.error;
      return formattedEvent;
    case "batch-documents-validated":
      formattedEvent.status = "success";
      formattedEvent.message = `All documents checked, ready for aggregation.`;
      return formattedEvent;
    case "batch-documents-extracted":
      formattedEvent.status = "success";
      formattedEvent.message = `All documents extracted successfully.`;
      return formattedEvent;
  }
  return formattedEvent;
};

export const getStatusIcon = (status: EventStatus) => {
  switch (status) {
    case "success":
      return CheckCircleIcon;
    case "error":
      return XCircleIcon;
    case "info":
    default:
      return InfoIcon;
  }
};

export const getStatusColor = (status: EventStatus) => {
  switch (status) {
    case "success":
      return "text-green-500";
    case "error":
      return "text-red-500";
    case "info":
    default:
      return "text-blue-500";
  }
};
