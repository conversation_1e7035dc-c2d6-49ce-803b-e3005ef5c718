import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, OneToMany, Unique } from "typeorm";
import { CustomsCountry, ProductType } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { SimplifiedState, State } from "./state.entity";
import { SimplifiedTradePartner, TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedProduct extends SimplifiedBaseEntity {
  @ApiProperty({ enum: CustomsCountry, default: CustomsCountry.CANADA })
  @Column({ type: "enum", enum: CustomsCountry, default: CustomsCountry.CANADA })
  customsCountry: CustomsCountry;

  @ApiProperty({ enum: ProductType, default: ProductType.REGULAR })
  @Column({ type: "enum", enum: ProductType, default: ProductType.REGULAR })
  productType: ProductType;

  @ApiProperty({ minLength: 1 })
  @Column()
  @Index("idx_product_part_number_trgm", { synchronize: false })
  partNumber: string;

  @ApiPropertyOptional({ minLength: 1 })
  @Column({ nullable: true })
  @Index("idx_product_sku_trgm", { synchronize: false })
  sku: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @Column({ nullable: true })
  @Index("idx_product_upc_trgm", { synchronize: false })
  upc: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @Column({ nullable: true })
  @Index("idx_product_vendor_part_number_trgm", { synchronize: false })
  vendorPartNumber: string | null;

  @ApiProperty({ minLength: 1 })
  @Column()
  description: string;

  @ApiProperty({ minLength: 10, maxLength: 10 })
  @Column()
  @Index("idx_product_hscode_trgm", { synchronize: false })
  hsCode: string;
}

@Entity()
@Unique("UNIQUE_PRODUCT", ["customsCountry", "partNumber", "sku", "upc", "vendor", "origin", "organization"])
export class Product extends SimplifiedProduct {
  @ApiProperty({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.vendorProducts, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  vendor: TradePartner;

  @ApiProperty({ type: () => SimplifiedCountry })
  @ManyToOne((type) => Country, (country) => country.products, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  origin: Country;

  @ApiPropertyOptional({ type: () => SimplifiedState })
  @ManyToOne((type) => State, null, {
    nullable: true,
    onDelete: "SET NULL"
  })
  originState: State | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.manufacturerProducts, {
    nullable: true,
    onDelete: "SET NULL"
  })
  manufacturer: TradePartner | null;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.products, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdProducts, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedProducts, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.product)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;

  @Column({ type: "tsvector", select: false, nullable: true })
  @Index("idx_product_search_vector", { synchronize: false })
  search_vector: string;
}
