import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import {
  ContainerType,
  CustomsCountry,
  CustomsStatus,
  QuantityUOM,
  ShipmentMode,
  ShipmentStatus,
  TrackingStatus,
  VolumeUOM,
  WeightUOM
} from "../types/shipment.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoice } from "./commercial-invoice.entity";
import { Container, SimplifiedContainer } from "./container.entity";
import { Document } from "./document.entity";
import { File } from "./file.entity";
import { Importer, SimplifiedImporter } from "./importer.entity";
import { Location, SimplifiedLocation } from "./location.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { TrackingHistory } from "./tracking-history.entity";
import { SimplifiedTradePartner, TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedShipment extends SimplifiedBaseEntity {
  @ApiProperty({
    type: "boolean",
    default: false,
    description:
      "Whether the shipment requires entry re-upload to Candata. This flag should only be used when Customs Status is on or after Live."
  })
  @Column({ type: "boolean", default: false })
  requiresReupload: boolean;

  @ApiProperty({ enum: CustomsCountry, default: CustomsCountry.CANADA })
  @Column({ type: "enum", enum: CustomsCountry, default: CustomsCountry.CANADA })
  customsCountry: CustomsCountry;

  @ApiProperty({ enum: ShipmentStatus, default: ShipmentStatus.NEW })
  @Column({ type: "enum", enum: ShipmentStatus, default: ShipmentStatus.NEW })
  status: ShipmentStatus;

  /** @deprecated Use `trackingStatus` in `containers` instead. */
  @ApiProperty({ enum: TrackingStatus, default: TrackingStatus.OFFLINE, deprecated: true })
  @Column({
    type: "enum",
    enum: TrackingStatus,
    default: TrackingStatus.OFFLINE
  })
  trackingStatus: TrackingStatus;

  @ApiProperty({ enum: ShipmentMode })
  @Column({ type: "enum", enum: ShipmentMode, nullable: true })
  modeOfTransport: ShipmentMode | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  etd: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  etaPort: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  etaPortString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  etaDestination: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  etaDestinationString: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  @Index("idx_shipment_mbl_trgm", { synchronize: false })
  mblNumber: string | null;

  @ApiProperty()
  @Column({ nullable: true })
  @Index("idx_shipment_hbl_trgm", { synchronize: false })
  hblNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  @Index("idx_shipment_cargo_control_number_trgm", { synchronize: false })
  cargoControlNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  carrierCode: string | null;

  /** @deprecated Use `containerNumber` in `containers` instead. */
  @ApiPropertyOptional({ deprecated: true })
  @Column({ nullable: true })
  containerNumber: string | null;

  /** @deprecated Use `containerType` in `containers` instead. */
  @ApiPropertyOptional({ enum: ContainerType, deprecated: true })
  @Column({ type: "enum", enum: ContainerType, nullable: true })
  containerType: ContainerType | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 999
  })
  @Column({ type: "real", nullable: true })
  volume: number | null;

  @ApiPropertyOptional({ enum: VolumeUOM })
  @Column({ type: "enum", enum: VolumeUOM, nullable: true })
  volumeUOM: VolumeUOM | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  weight: number | null;

  @ApiPropertyOptional({ enum: WeightUOM })
  @Column({ type: "enum", enum: WeightUOM, nullable: true })
  weightUOM: WeightUOM | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0, maximum: 999999 })
  @Column({ type: "integer", nullable: true })
  quantity: number | null;

  @ApiPropertyOptional({ enum: QuantityUOM })
  @Column({ type: "enum", enum: QuantityUOM, nullable: true })
  quantityUOM: QuantityUOM | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  pickupLfd: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  pickupLfdString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  pickupDate: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  pickupDateString: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  pickupNumber: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  returnLfd: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  returnLfdString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  returnDate: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  returnDateString: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  vessel: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  voyageNumber: string | null;

  @ApiProperty({
    enum: CustomsStatus,
    default: CustomsStatus.PENDING_COMMERCIAL_INVOICE
  })
  @Column({
    type: "enum",
    enum: CustomsStatus,
    default: CustomsStatus.PENDING_COMMERCIAL_INVOICE
  })
  customsStatus: CustomsStatus;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  @Index("idx_shipment_transaction_number_trgm", { synchronize: false })
  transactionNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  @Index("idx_shipment_customs_file_number_trgm", { synchronize: false })
  customsFileNumber: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  releaseDate: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  surrenderDate: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  adviceNoteDate: Date | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  portCode: string | null;
  // portNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  portOfExit: string | null;

  // @ApiPropertyOptional()
  // @Column({ nullable: true })
  // orderNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  subLocation: string | null;

  // @ApiPropertyOptional()
  // @Column({ nullable: true })
  // directShipmentMode: string | null;

  // @ApiPropertyOptional()
  // @Column({ nullable: true })
  // directShipmentLocation: string | null;

  // @ApiPropertyOptional({ enum: Currency })
  // @Column({ type: 'enum', enum: Currency, nullable: true })
  // currency: Currency | null;

  @ApiPropertyOptional({ type: "number" })
  @Column({ nullable: false })
  organizationId: number;
}

// TODO: Add shipment constraints
@Entity()
export class Shipment extends SimplifiedShipment {
  @ApiProperty({ type: () => [SimplifiedContainer] })
  @OneToMany((type) => Container, (container) => container.shipment, {
    cascade: true
  })
  containers: Array<Container>;

  @ApiPropertyOptional({
    type: () => SimplifiedLocation,
    description: "Port of Discharge"
  })
  @ManyToOne((type) => Location, (location) => location.portOfDischargeShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  portOfDischarge: Location | null;

  @ApiPropertyOptional({
    type: () => SimplifiedLocation,
    description: "Port of Loading"
  })
  @ManyToOne((type) => Location, (location) => location.portOfLoadingShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  portOfLoading: Location | null;

  @ApiPropertyOptional({
    type: () => SimplifiedLocation,
    description: "Place of Delivery"
  })
  @ManyToOne((type) => Location, (location) => location.placeOfDeliveryShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  placeOfDelivery: Location | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.carrierShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  carrier: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.manufacturerShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  manufacturer: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.shipperShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipper: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedImporter })
  @ManyToOne((type) => Importer, (importer) => importer.shipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  importer: Importer | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.consigneeShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  consignee: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.forwarderShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  forwarder: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.truckerShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  trucker: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne((type) => TradePartner, (partner) => partner.pickupLocationShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  pickupLocation: TradePartner | null;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.shipments, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedShipments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [Document] })
  @OneToMany((type) => Document, (document) => document.shipment, {
    onDelete: "SET NULL"
  })
  documents: Array<Document>;

  @ApiProperty({ type: () => [File] })
  @OneToMany((type) => File, (file) => file.shipment, {
    onDelete: "SET NULL"
  })
  files: Array<File>;

  //hidden fields
  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.shipment)
  commercialInvoices: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => TrackingHistory, (history) => history.shipment)
  trackingHistories: Promise<Array<TrackingHistory>>;

  @Column({ type: "tsvector", select: false, nullable: true })
  @Index("idx_shipment_search_vector", { synchronize: false })
  search_vector: string;
}
