import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  Max,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { Product } from "../entities";
import { CustomsCountry, ProductColumn, ProductType, ShipmentMode, ShipmentStatus } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetProductsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: ProductColumn })
  @IsOptional()
  @IsEnum(ProductColumn)
  sortBy?: ProductColumn;

  @ApiPropertyOptional({ enum: CustomsCountry })
  @IsOptional()
  @IsEnum(CustomsCountry)
  customsCountry?: CustomsCountry;

  @ApiPropertyOptional({ enum: ProductType })
  @IsOptional()
  @IsEnum(ProductType)
  productType?: ProductType;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  partNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  sku?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  upc?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorPartNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  description?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  hsCode?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  vendorId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  originId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetProductsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Product] })
  products: Array<Product>;
}

export class CreateProductDto {
  @ApiPropertyOptional({ enum: CustomsCountry, default: CustomsCountry.CANADA })
  @IsOptional()
  @IsEnum(CustomsCountry)
  customsCountry?: CustomsCountry;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  partNumber: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  sku?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  upc?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorPartNumber?: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  description: string;

  @ApiProperty({ minLength: 10, maxLength: 10, pattern: "^[0-9]{10}$" })
  @IsNotEmpty()
  @IsString()
  // @MinLength(10)
  // @MaxLength(10)
  @Matches(/^[0-9]{10}$/, { message: "HS code must be 10 digits" })
  hsCode: string;

  @ApiProperty({ minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  vendorId: number;

  @ApiProperty({ minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  originId: number;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  originStateId?: number;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number;
}

export class EditProductDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  partNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  sku?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  upc?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorPartNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  description?: string;

  @ApiPropertyOptional({ minLength: 10, maxLength: 10, pattern: "^[0-9]{10}$" })
  @IsOptional()
  @IsString()
  // @MinLength(10)
  // @MaxLength(10)
  @Matches(/^[0-9]{10}$/, { message: "HS code must be 10 digits" })
  hsCode?: string;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  vendorId?: number;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  originId?: number;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  originStateId?: number;

  @ApiPropertyOptional({ minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number;
}

export class EditProductWithIdDto extends EditProductDto {
  @ApiProperty({ minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateProductsDto {
  @ApiPropertyOptional({ type: () => [CreateProductDto] })
  @Type((type) => CreateProductDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateProductDto>;

  @ApiPropertyOptional({ type: () => [EditProductWithIdDto] })
  @Type((type) => EditProductWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditProductWithIdDto>;

  @ApiPropertyOptional({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateProductsResponseDto {
  @ApiProperty({ type: () => [Product] })
  products: Array<Product>;
}

export class GetRelatedCommercialInvoiceLinesDto {
  @ApiPropertyOptional({ type: "integer", minimum: 0, default: 0 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    maximum: 100,
    default: 10
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  limit?: number;
}

export class RelatedCommercialInvoiceLineDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  id: number;

  @ApiProperty({ type: "integer", minimum: 1 })
  commercialInvoiceId: number;

  @ApiProperty({ type: "integer", minimum: 1 })
  shipmentId: number;

  @ApiProperty({ enum: ShipmentStatus })
  status: ShipmentStatus;

  @ApiProperty({ enum: ShipmentMode })
  modeOfTransport: ShipmentMode;

  @ApiProperty({ type: "string" })
  hblNumber: string;

  @ApiPropertyOptional({ type: "string" })
  cargoControlNumber?: string | null;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9]{14}$" })
  transactionNumber?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  etaDestination?: Date | null;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  portOfLoading?: string | null;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  placeOfDelivery?: string | null;

  @ApiPropertyOptional({ type: "integer" })
  vfd?: number | null;

  @ApiPropertyOptional({ type: "integer" })
  tt?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0, maximum: 999999 })
  quantity?: number | null;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  unitOfMeasure?: string | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  unitPrice?: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  totalLineValue?: number | null;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  currency?: string | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  lineTotalDutiesAndTaxes?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0, maximum: 9999 })
  // grossWeight?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0, maximum: 9999 })
  // netWeight?: number | null;

  // @ApiPropertyOptional({ enum: WeightUOM })
  // weightUOM?: WeightUOM | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // totalLineValue?: number | null;

  // @ApiPropertyOptional({ type: 'string', maxLength: 3 })
  // currency?: string | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // totalLineValueCad?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // dutyCad?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // valueForTax?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // gst?: number | null;

  // @ApiPropertyOptional({ type: 'number', format: 'float', minimum: 0 })
  // dutyAndTax?: number | null;
}

export class GetRelatedCommercialInvoiceLinesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [RelatedCommercialInvoiceLineDto] })
  commercialInvoiceLines: Array<RelatedCommercialInvoiceLineDto>;
}
