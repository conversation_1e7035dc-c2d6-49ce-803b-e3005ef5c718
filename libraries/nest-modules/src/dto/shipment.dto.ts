import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  isBooleanString,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  Matches,
  Max,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { Shipment } from "../entities/shipment.entity";
import { VfdCode } from "../types";
import {
  ContainerType,
  CustomsCountry,
  CustomsStatus,
  QuantityUOM,
  ShipmentColumn,
  ShipmentMode,
  ShipmentStatus,
  TrackingStatus,
  VolumeUOM,
  WeightUOM
} from "../types/shipment.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { ValidateCommercialInvoiceComplianceResponseDto } from "./commercial-invoice.dto";
import { ShipmentContainerDto } from "./shipment-container.dto";

export class GetShipmentsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: ShipmentColumn, default: ShipmentColumn.id })
  @IsOptional()
  @IsEnum(ShipmentColumn)
  sortBy?: ShipmentColumn;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && isBooleanString(value?.trim()?.toLowerCase())
      ? value?.trim()?.toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  requiresReupload?: boolean;

  @ApiPropertyOptional({ enum: CustomsCountry })
  @IsOptional()
  @IsEnum(CustomsCountry)
  customsCountry?: CustomsCountry;

  @ApiPropertyOptional({ enum: ShipmentStatus })
  @IsOptional()
  @IsEnum(ShipmentStatus)
  status?: ShipmentStatus;

  @ApiPropertyOptional({ enum: TrackingStatus })
  @IsOptional()
  @IsEnum(TrackingStatus)
  trackingStatus?: TrackingStatus;

  @ApiPropertyOptional({ enum: ShipmentMode })
  @IsOptional()
  @IsEnum(ShipmentMode)
  modeOfTransport?: ShipmentMode;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etdFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etdTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaPortFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaPortTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaDestinationFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaDestinationTo?: Date;

  @ApiPropertyOptional({ minLength: 4, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^0[0-9]{3}$/)
  portCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  portOfExit?: string;

  @ApiPropertyOptional({ minLength: 1, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^[0-9]{4}$/)
  subLocation?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  mblNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  hblNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  containerNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  cargoControlNumber?: string;

  @ApiPropertyOptional({ enum: ContainerType })
  @IsOptional()
  @IsEnum(ContainerType)
  containerType?: ContainerType;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 999
  })
  @Type((type) => Number)
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(999)
  volume?: number;

  @ApiPropertyOptional({ enum: VolumeUOM })
  @IsOptional()
  @IsEnum(VolumeUOM)
  volumeUOM?: VolumeUOM;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 99999999
  })
  @Type((type) => Number)
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(99999999)
  weight?: number;

  @ApiPropertyOptional({ enum: WeightUOM })
  @IsOptional()
  @IsEnum(WeightUOM)
  weightUOM?: WeightUOM;

  @ApiPropertyOptional({ type: "integer", minimum: 0, maximum: 999999 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(999999)
  quantity?: number;

  @ApiPropertyOptional({ enum: QuantityUOM })
  @IsOptional()
  @IsEnum(QuantityUOM)
  quantityUOM?: QuantityUOM;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupLfdFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupLfdTo?: Date;

  // @ApiPropertyOptional({ minLength: 1 })
  // @IsOptional()
  // @IsString()
  // @MinLength(1)
  // progress?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupDateTo?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  pickupNumber?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnDateTo?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  returnLfdFrom?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  returnLfdTo?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vessel?: string;

  @ApiPropertyOptional({ enum: CustomsStatus })
  @IsOptional()
  @IsEnum(CustomsStatus)
  customsStatus?: CustomsStatus;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  transactionNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  customsFileNumber?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  releaseDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  releaseDateTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  surrenderDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  surrenderDateTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  adviceNoteDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  adviceNoteDateTo?: Date;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Discharge"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfDischargeId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Loading"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfLoadingId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Place of Delivery"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  placeOfDeliveryId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  carrierId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  shipperId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  importerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  consigneeId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  forwarderId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  truckerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  pickupLocationId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetShipmentsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Shipment] })
  shipments: Array<Shipment>;
}

export class CreateShipmentDto {
  @ApiPropertyOptional({ enum: CustomsCountry, default: CustomsCountry.CANADA })
  @IsOptional()
  @IsEnum(CustomsCountry)
  customsCountry?: CustomsCountry;

  @ApiPropertyOptional({ enum: ShipmentStatus, default: ShipmentStatus.NEW })
  @IsOptional()
  @IsEnum(ShipmentStatus)
  status?: ShipmentStatus;

  @ApiProperty({ enum: ShipmentMode })
  @IsNotEmpty()
  @IsEnum(ShipmentMode)
  modeOfTransport: ShipmentMode;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etd?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaPort?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  etaPortString?: string | null;

  @ApiPropertyOptional({
    type: "string",
    format: "date-time",
    description: "If containers exist, this field will be overwritten by the oldest container"
  })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaDestination?: Date | null;

  @ApiPropertyOptional({
    type: "string",
    description: "If containers exist, this field will be overwritten by the oldest container"
  })
  @IsOptional()
  @IsString()
  etaDestinationString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  mblNumber?: string | null;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  hblNumber: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  cargoControlNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  carrierCode?: string | null;

  /** @deprecated Use `containers` instead. */
  @ApiPropertyOptional({ minLength: 1, deprecated: true })
  @IsOptional()
  @IsString()
  @MinLength(1)
  containerNumber?: string | null;

  @ApiPropertyOptional({ type: () => [ShipmentContainerDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayUnique((c) => c.containerNumber)
  @Type(() => ShipmentContainerDto)
  containers?: ShipmentContainerDto[];

  /** @deprecated Use `containers` instead. */
  @ApiPropertyOptional({ enum: ContainerType, deprecated: true })
  @IsOptional()
  @IsEnum(ContainerType)
  containerType?: ContainerType | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 999
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(999)
  volume?: number | null;

  @ApiPropertyOptional({ enum: VolumeUOM })
  @IsOptional()
  @IsEnum(VolumeUOM)
  volumeUOM?: VolumeUOM | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 99999999
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(99999999)
  weight?: number | null;

  @ApiPropertyOptional({ enum: WeightUOM })
  @IsOptional()
  @IsEnum(WeightUOM)
  weightUOM?: WeightUOM | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0, maximum: 999999 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(999999)
  quantity?: number | null;

  @ApiPropertyOptional({ enum: QuantityUOM })
  @IsOptional()
  @IsEnum(QuantityUOM)
  quantityUOM?: QuantityUOM | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupLfd?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  pickupLfdString?: string | null;

  // @ApiPropertyOptional({ minLength: 1 })
  // @IsOptional()
  // @IsString()
  // @MinLength(1)
  // progress?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupDate?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  pickupDateString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  pickupNumber?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnLfd?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  returnLfdString?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnDate?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  returnDateString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vessel?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  voyageNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  transactionNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  customsFileNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^0[0-9]{3}$/)
  portCode?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  portOfExit?: string | null;

  @ApiPropertyOptional({ minLength: 1, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^[0-9]{4}$/)
  subLocation?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  releaseDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  surrenderDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  adviceNoteDate?: Date | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Discharge"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfDischargeId?: number | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Loading"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfLoadingId?: number | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Place of Delivery"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  placeOfDeliveryId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  carrierId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  shipperId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  importerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  consigneeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  forwarderId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  truckerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  pickupLocationId?: number | null;

  @ApiPropertyOptional({ type: "array", items: { type: "integer" } })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Min(1, { each: true })
  documentIds?: number[] | null;
}

export class EditShipmentDto {
  @ApiPropertyOptional({ enum: ShipmentStatus, default: ShipmentStatus.NEW })
  @IsOptional()
  @IsEnum(ShipmentStatus)
  status?: ShipmentStatus;

  @ApiPropertyOptional({ enum: ShipmentMode })
  @IsOptional()
  @IsEnum(ShipmentMode)
  modeOfTransport?: ShipmentMode | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etd?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaPort?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  etaPortString?: string | null;

  @ApiPropertyOptional({
    type: "string",
    format: "date-time",
    description: "If containers exist, this field will be overwritten by the oldest container"
  })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  etaDestination?: Date | null;

  @ApiPropertyOptional({
    type: "string",
    description: "If containers exist, this field will be overwritten by the oldest container"
  })
  @IsOptional()
  @IsString()
  etaDestinationString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  mblNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  hblNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  cargoControlNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  carrierCode?: string | null;

  /** @deprecated Use `containers` instead. */
  @ApiPropertyOptional({ minLength: 1, deprecated: true })
  @IsOptional()
  @IsString()
  @MinLength(1)
  containerNumber?: string | null;

  @ApiPropertyOptional({ type: () => [ShipmentContainerDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayUnique((c) => c.containerNumber)
  @Type(() => ShipmentContainerDto)
  containers?: ShipmentContainerDto[];

  /** @deprecated Use `containers` instead. */
  @ApiPropertyOptional({ enum: ContainerType })
  @IsOptional()
  @IsEnum(ContainerType)
  containerType?: ContainerType | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 999
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(999)
  volume?: number | null;

  @ApiPropertyOptional({ enum: VolumeUOM })
  @IsOptional()
  @IsEnum(VolumeUOM)
  volumeUOM?: VolumeUOM | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 99999999
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(99999999)
  weight?: number | null;

  @ApiPropertyOptional({ enum: WeightUOM })
  @IsOptional()
  @IsEnum(WeightUOM)
  weightUOM?: WeightUOM | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0, maximum: 999999 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(999999)
  quantity?: number | null;

  @ApiPropertyOptional({ enum: QuantityUOM })
  @IsOptional()
  @IsEnum(QuantityUOM)
  quantityUOM?: QuantityUOM | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupLfd?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  pickupLfdString?: string | null;

  // @ApiPropertyOptional({ minLength: 1 })
  // @IsOptional()
  // @IsString()
  // @MinLength(1)
  // progress?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  pickupDate?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  pickupDateString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  pickupNumber?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnLfd?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  returnLfdString?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  returnDate?: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  returnDateString?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vessel?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  voyageNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  transactionNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  customsFileNumber?: string | null;

  @ApiPropertyOptional({ minLength: 1, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^0[0-9]{3}$/)
  portCode?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  portOfExit?: string | null;

  @ApiPropertyOptional({ minLength: 1, maxLength: 4 })
  @IsOptional()
  @IsString()
  @Length(4, 4)
  @Matches(/^[0-9]{4}$/)
  subLocation?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  releaseDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  surrenderDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type((type) => Date)
  @IsOptional()
  @IsDate()
  adviceNoteDate?: Date | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Discharge"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfDischargeId?: number | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Port of Loading"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  portOfLoadingId?: number | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Place of Delivery"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  placeOfDeliveryId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  carrierId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  manufacturerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  shipperId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  importerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  consigneeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  forwarderId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  truckerId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  pickupLocationId?: number | null;

  @ApiPropertyOptional({ type: "array", items: { type: "integer" } })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Min(1, { each: true })
  documentIds?: number[] | null;
}

export class ValidateShipmentComplianceResponseDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  shipmentId: number;

  @ApiProperty({
    type: "boolean",
    description: "True if no commercial invoice is associated with the shipment"
  })
  noCommercialInvoice: boolean;

  @ApiProperty({
    type: "enum",
    enum: ShipmentColumn,
    isArray: true,
    description: "List of missing fields required for entry submission"
  })
  missingFields: Array<ShipmentColumn>;

  @ApiProperty({
    type: () => [ValidateCommercialInvoiceComplianceResponseDto],
    description: "List of non-compliant commercial invoices"
  })
  nonCompliantInvoices: Array<ValidateCommercialInvoiceComplianceResponseDto>;
}

export class DutySummaryLineDto {
  @ApiProperty({ type: "integer" })
  sequence: number;

  @ApiProperty({ type: "string" })
  description: string;

  @ApiProperty({ type: "string", minLength: 10, maxLength: 10 })
  hsCode: string;

  @ApiProperty({ type: "number", format: "float" })
  quantity: number;

  @ApiProperty({ type: "string", maxLength: 3 })
  quantityUOM: string;

  @ApiPropertyOptional({ enum: VfdCode })
  vfd?: VfdCode | null;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  simaCode?: string | null;

  @ApiProperty({ type: "string", maxLength: 3 })
  currency: string;

  @ApiProperty({ type: "number", format: "float" })
  valueForDuty: number;

  @ApiProperty({ type: "number", format: "float" })
  antiDumping: number;

  @ApiProperty({ type: "number", format: "float" })
  countervailing: number;

  @ApiProperty({ type: "number", format: "float" })
  customsDuties: number;

  @ApiProperty({ type: "number", format: "float" })
  exciseDuties: number;

  @ApiProperty({ type: "number", format: "float" })
  exciseTax: number;

  @ApiProperty({ type: "number", format: "float" })
  gst: number;

  @ApiProperty({ type: "number", format: "float" })
  pstHst: number;

  @ApiProperty({ type: "number", format: "float" })
  provincialAlcoholTax: number;

  @ApiProperty({ type: "number", format: "float" })
  provincialCannabisExciseDuty: number;

  @ApiProperty({ type: "number", format: "float" })
  provincialTobaccoTax: number;

  @ApiProperty({ type: "number", format: "float" })
  safeguard: number;

  @ApiProperty({ type: "number", format: "float" })
  surtax: number;

  @ApiProperty({ type: "number", format: "float" })
  totalDutiesAndTaxes: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // customsDutyRate: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // etRate: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // gstRate: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // valueForDuty: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // valueForCurrencyConversion: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // customsDuties: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // simaAssessment: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // exciseTax: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // valueForTax: number;

  // @ApiProperty({ type: 'number', format: 'float' })
  // gst: number;
}

export class GetShipmentDutySummaryResponseDto {
  @ApiProperty({ type: () => [DutySummaryLineDto] })
  dutySummaryLines: Array<DutySummaryLineDto>;
}

export class CustomsActivityDto {
  @ApiProperty({ type: "string", format: "date-time" })
  responseDate: string;

  @ApiProperty({ type: "string", pattern: "^[0-9]{14}$" })
  transactionNumber: string;

  @ApiProperty({ type: "string", example: "Released" })
  event: string;

  @ApiPropertyOptional({
    type: "string",
    minLength: 4,
    maxLength: 4,
    pattern: "^0[0-9]{3}$"
  })
  portCode?: string | null;

  @ApiPropertyOptional({
    type: "string",
    minLength: 4,
    maxLength: 4,
    pattern: "^[0-9]{4}$"
  })
  subLocationCode?: string | null;
}

export class GetShipmentCustomsActivitiesResponseDto {
  @ApiProperty({ type: () => [CustomsActivityDto] })
  customsActivities: Array<CustomsActivityDto>;
}
