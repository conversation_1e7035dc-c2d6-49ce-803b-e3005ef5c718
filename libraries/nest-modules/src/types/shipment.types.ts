import { FindOptionsRelations } from "typeorm";
import { Shipment } from "../entities";

export enum CustomsCountry {
  CANADA = "ca",
  US = "us"
}

export enum ShipmentStatus {
  NEW = "new",
  IN_TRANSIT = "in-transit",
  AT_PORT = "at-port",
  ON_RAIL = "on-rail",
  AT_TERMINAL = "at-terminal",
  PICKED_UP = "picked-up",
  COMPLETED = "completed"
}

export enum ShipmentMode {
  AIR = "air",
  OCEAN_FCL = "ocean-fcl",
  OCEAN_LCL = "ocean-lcl",
  LAND = "land",
  SMALL_PACKAGE = "small-package"
}

/**
 * Enum for container types
 */
export enum ContainerType {
  // general purpose
  FCL_20GP = "fcl-20gp",
  FCL_40GP = "fcl-40gp",

  // high cube
  FCL_40HQ = "fcl-40hq",
  FCL_45HQ = "fcl-45hq",

  // refrigerated
  FCL_20RF = "fcl-20rf",
  FCL_40RF = "fcl-40rf",

  // refrigerated high cube
  FCL_20RH = "fcl-20rh",
  FCL_40RH = "fcl-40rh",
  FCL_45RH = "fcl-45rh",

  // open top
  FCL_20OT = "fcl-20ot",
  FCL_40OT = "fcl-40ot",

  // flat rack
  FCL_20FR = "fcl-20fr",
  FCL_40FR = "fcl-40fr"
}

export enum VolumeUOM {
  CBM = "cbm",
  CFT = "cft"
}

export enum WeightUOM {
  METRIC_CARAT = "ctm",
  DECITON = "dtn",
  GRAM = "grm",
  HECTOGRAM = "hgm",
  KILOGRAM = "kgm",
  KILOGRAM_NAMED_SUBSTANCE = "kns",
  KILOGRAM_AIR_DRY = "ksd",
  KILOTON = "ktn",
  POUND = "lbr",
  MILLIGRAM = "mgm",
  GRAMS_ODP_WEIGHTED = "odg",
  KILOGRAM_ODP_WEIGHTED = "odk",
  MILLIGRAM_ODP_WEIGHTED = "odm",
  METRIC_TON = "tne",
  TONNE_AIR_DRY = "tsd"
}

export enum QuantityUOM {
  BOX = "box",
  PACK = "pk",
  PALLET = "pal",
  CARTON = "ctn",
  BAG = "bg",
  CASE = "cs",
  ROLL = "rl",
  BOTTLE = "btl",
  DRUM = "drm",
  PACKAGE = "pkg"
}

export enum CustomsStatus {
  /**
   * No commercial invoice is created for the shipment.
   */
  PENDING_COMMERCIAL_INVOICE = "pending-commercial-invoice",
  /**
   * Commercial invoice exists for the shipment, but some compliance errors (e.g. missing required fields, compliance rule errors, etc.) prevent it from being submitted.
   */
  PENDING_CONFIRMATION = "pending-confirmation",
  /**
   * Shipment has no error that prevents it from being submitted but the time of submission is not yet reached. This status is not applicable for truck shipments.
   */
  PENDING_ARRIVAL = "pending-arrival",
  /**
   * Shipment entry is uploaded to Candata and waiting for backoffice admin to submit it manually.
   */
  LIVE = "live",
  /**
   * Shipment entry is submitted manually on Candata.
   */
  ENTRY_SUBMITTED = "entry-submitted",
  /**
   * Shipment is submitted and is accepted by CBSA.
   */
  ENTRY_ACCEPTED = "entry-accepted",
  /**
   * Shipment is selected by CBSA for examination.
   */
  EXAM = "exam",
  /**
   * Shipment is released by CBSA.
   */
  RELEASED = "released",
  /**
   * Shipment accounting is completed.
   */
  ACCOUNTING_COMPLETED = "accounting-completed"
}

export enum TrackingStatus {
  // Tracking is in progress
  TRACKING = "tracking",
  // Shipment is found online
  ONLINE = "online",
  // Shipment is not found online
  OFFLINE = "offline",
  // Error occurred while tracking
  ERROR = "error"
}

export enum Currency {
  AED = "aed",
  AFN = "afn",
  ALL = "all",
  AMD = "amd",
  ANG = "ang",
  AOA = "aoa",
  ARS = "ars",
  AUD = "aud",
  AWG = "awg",
  AZN = "azn",
  BAM = "bam",
  BBD = "bbd",
  BDT = "bdt",
  BGN = "bgn",
  BHD = "bhd",
  BIF = "bif",
  BMD = "bmd",
  BND = "bnd",
  BOB = "bob",
  BOV = "bov",
  BRL = "brl",
  BSD = "bsd",
  BTN = "btn",
  BWP = "bwp",
  BYN = "byn",
  BZD = "bzd",
  CAD = "cad",
  CDF = "cdf",
  CHE = "che",
  CHF = "chf",
  CHW = "chw",
  CLF = "clf",
  CLP = "clp",
  CNY = "cny",
  COP = "cop",
  COU = "cou",
  CRC = "crc",
  CUC = "cuc",
  CUP = "cup",
  CVE = "cve",
  CZK = "czk",
  DJF = "djf",
  DKK = "dkk",
  DOP = "dop",
  DZD = "dzd",
  EGP = "egp",
  ERN = "ern",
  ETB = "etb",
  EUR = "eur",
  FJD = "fjd",
  FKP = "fkp",
  GBP = "gbp",
  GEL = "gel",
  GHS = "ghs",
  GIP = "gip",
  GMD = "gmd",
  GNF = "gnf",
  GTQ = "gtq",
  GYD = "gyd",
  HKD = "hkd",
  HNL = "hnl",
  HRK = "hrk",
  HTG = "htg",
  HUF = "huf",
  IDR = "idr",
  ILS = "ils",
  INR = "inr",
  IQD = "iqd",
  IRR = "irr",
  ISK = "isk",
  JMD = "jmd",
  JOD = "jod",
  JPY = "jpy",
  KES = "kes",
  KGS = "kgs",
  KHR = "khr",
  KMF = "kmf",
  KPW = "kpw",
  KRW = "krw",
  KWD = "kwd",
  KYD = "kyd",
  KZT = "kzt",
  LAK = "lak",
  LBP = "lbp",
  LKR = "lkr",
  LRD = "ldr",
  LSL = "lsl",
  LYD = "lyd",
  MAD = "mad",
  MDL = "mdl",
  MGA = "mga",
  MKD = "mkd",
  MMK = "mmk",
  MNT = "mnt",
  MOP = "mop",
  MRO = "mro",
  MUR = "mur",
  MVR = "mvr",
  MWK = "mwk",
  MXN = "mxn",
  MXV = "mxv",
  MYR = "myr",
  MZN = "mzn",
  NAD = "nad",
  NGN = "ngn",
  NIO = "nio",
  NOK = "nok",
  NPR = "npr",
  NZD = "nzd",
  OMR = "omr",
  PAB = "pab",
  PEN = "pen",
  PGK = "pgk",
  PHP = "php",
  PKR = "pkr",
  PLN = "pln",
  PYG = "pyg",
  QAR = "qar",
  RON = "ron",
  RSD = "rsd",
  RUB = "rub",
  RWF = "rwf",
  SAR = "sar",
  SBD = "sbd",
  SCR = "scr",
  SDG = "sdg",
  SEK = "sek",
  SGD = "sgd",
  SHP = "shp",
  SLL = "sll",
  SOS = "sos",
  SRD = "srd",
  SSP = "ssp",
  STN = "stn",
  SVC = "svc",
  SYP = "syp",
  SZL = "szl",
  THB = "thb",
  TJS = "tjs",
  TMT = "tmt",
  TND = "tnd",
  TOP = "top",
  TRY = "try",
  TTD = "ttd",
  TWD = "twd",
  TZS = "tzs",
  UAH = "uah",
  UGX = "ugx",
  USD = "usd",
  USN = "usn",
  UYI = "uyi",
  UYU = "uyu",
  UZS = "uzs",
  VEF = "vef",
  VND = "vnd",
  VUV = "vuv",
  WST = "wst",
  XAF = "xaf",
  XAG = "xag",
  XAU = "xau",
  XBA = "xba",
  XBB = "xbb",
  XBC = "xbc",
  XBD = "xbd",
  XCD = "xcd",
  XDR = "xdr",
  XOF = "xof",
  XPD = "xpd",
  XPF = "xpf",
  XPT = "xpt",
  XSU = "xsu",
  XTS = "xts",
  XUA = "xua",
  XXX = "xxx",
  YER = "yer",
  ZAR = "zar",
  ZMW = "zmw",
  ZWL = "zwl"
}

export enum ShipmentColumn {
  id = "id",
  requiresReupload = "requiresReupload",
  customsCountry = "customsCountry",
  status = "status",
  /** @deprecated Use `trackingStatus` in `containers` instead. */
  trackingStatus = "trackingStatus",
  modeOfTransport = "modeOfTransport",
  etd = "etd",
  etaPort = "etaPort",
  etaPortString = "etaPortString",
  etaDestination = "etaDestination",
  etaDestinationString = "etaDestinationString",
  mblNumber = "mblNumber",
  hblNumber = "hblNumber",
  cargoControlNumber = "cargoControlNumber",
  carrierCode = "carrierCode",
  /** @deprecated Use `containerNumber` in `containers` instead. */
  containerNumber = "containerNumber",
  /** @deprecated Use `containerType` in `containers` instead. */
  containerType = "containerType",
  volume = "volume",
  volumeUOM = "volumeUOM",
  weight = "weight",
  weightUOM = "weightUOM",
  quantity = "quantity",
  quantityUOM = "quantityUOM",
  pickupLfd = "pickupLfd",
  pickupLfdString = "pickupLfdString",
  pickupDate = "pickupDate",
  pickupDateString = "pickupDateString",
  pickupNumber = "pickupNumber",
  returnLfd = "returnLfd",
  returnLfdString = "returnLfdString",
  returnDate = "returnDate",
  returnDateString = "returnDateString",
  vessel = "vessel",
  voyageNumber = "voyageNumber",
  customsStatus = "customsStatus",
  transactionNumber = "transactionNumber",
  customsFileNumber = "customsFileNumber",
  portCode = "portCode",
  portOfExit = "portOfExit",
  subLocation = "subLocation",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  portOfDischargeId = "portOfDischargeId",
  portOfLoadingId = "portOfLoadingId",
  placeOfDeliveryId = "placeOfDeliveryId",
  carrierId = "carrierId",
  manufacturerId = "manufacturerId",
  shipperId = "shipperId",
  importerId = "importerId",
  consigneeId = "consigneeId",
  forwarderId = "forwarderId",
  truckerId = "truckerId",
  pickupLocationId = "pickupLocationId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_SHIPMENT_RELATIONS: FindOptionsRelations<Shipment> = {
  containers: true,
  portOfDischarge: true,
  portOfLoading: true,
  placeOfDelivery: true,
  carrier: true,
  manufacturer: true,
  shipper: true,
  importer: true,
  consignee: true,
  forwarder: true,
  trucker: true,
  pickupLocation: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true
};

export const SHIPMENT_ENUM_KEYS = [
  "customsCountry",
  "status",
  "trackingStatus",
  "modeOfTransport",
  "containerType",
  "volumeUOM",
  "weightUOM",
  "quantityUOM",
  "customsStatus"
];
export const SHIPMENT_REQUIRED_KEYS = [
  "requiresReupload",
  "customsCountry",
  "status",
  "trackingStatus",
  "modeOfTransport",
  "hblNumber",
  "customsStatus",
  "organization"
];
