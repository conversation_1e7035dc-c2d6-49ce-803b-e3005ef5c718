import { FindOptionsRelations } from "typeorm";
import { Product } from "../entities";

export enum ProductType {
  TEMPORARY = "temporary",
  REGULAR = "regular"
}

export enum ProductColumn {
  id = "id",
  customsCountry = "customsCountry",
  productType = "productType",
  partNumber = "partNumber",
  sku = "sku",
  upc = "upc",
  vendorPartNumber = "vendorPartNumber",
  description = "description",
  hsCode = "hsCode",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  vendorId = "vendorId",
  originId = "originId",
  manufacturerId = "manufacturerId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_PRODUCT_RELATIONS: FindOptionsRelations<Product> = {
  vendor: true,
  origin: true,
  originState: true,
  manufacturer: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true
};

export const PRODUCT_ENUM_KEYS = ["customsCountry", "productType"];

export const PRODUCT_REQUIRED_KEYS = [
  "customsCountry",
  "productType",
  "partNumber",
  "description",
  "hsCode",
  "vendor",
  "origin",
  "organization"
];

export const PRODUCT_CASE_INSENSITIVE_KEYS = ["partNumber", "sku", "vendorPartNumber"];
