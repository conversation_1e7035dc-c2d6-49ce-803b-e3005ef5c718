---
description: CloudWatch MCP email tracing and log analysis - finding emails and reading sequential logs
---

# CloudWatch MCP Email Tracing Guide

This rule applies when searching for emails or email processing in CloudWatch logs.

## Timestamp Filtering

### ✅ CORRECT @timestamp Syntax

```
to<PERSON><PERSON><PERSON>(@timestamp) >= 1751848249000 and to<PERSON><PERSON><PERSON>(@timestamp) <= 1751848250000
```

### ❌ INCORRECT Formats That Don't Work

- `@timestamp >= "2025-07-07T00:25:00.000Z"`
- `@timestamp >= "2025-07-07 00:25:00"`
- `datefloor(@timestamp, 1h) = datefloor(strptime("2025-07-07T00:30:00", "%Y-%m-%dT%H:%M:%S"), 1h)`
- `bin(@timestamp, 1h) = bin(1736225400000, 1h)`

### Getting Millisecond Values

First, find the timestamp in milliseconds:

```
fields @timestamp, toMill<PERSON>(@timestamp) as timestamp_millis, @message
| filter @message like /your_search_term/
| sort @timestamp asc
| limit 5
```

## Step-by-Step Email Tracing Process

### 1. Find When the Email Was Received

Search for the subject or identifier string within the log group `ClaroInfrastructureStack-PortalApiServiceTaskDefwebLogGroup603D6AA7-A8hmDo5Mgulz` within the last 6 hours, then 24 hours if you don't find any results:

```
fields @timestamp, @message
| filter @message like /your_email_subject_or_identifier/
| sort @timestamp asc
```

**Differentiate between email processing and other logs:**

**Email Processing Patterns:**

- `CoreAgentEmailSagaListener`
- `EMAIL_SAVED`
- `emailId: [number]`
- `subject:`
- `incoming email`
- `email.*received`
- `processing.*email`

**System/Automated Patterns (NOT email processing):**

- `CustomStatusService`
- `checking X shipments`
- `customs status check`
- Recurring automated tasks

> **Tip:** When searching, always start with a narrow time window around the suspected event. Only expand the time range if you do not find the relevant logs. This approach avoids overwhelming results and helps you quickly zero in on the email processing flow.

After identifying a candidate log line (e.g., `EMAIL_SAVED` or `CoreAgentEmailSagaListener`), use a timestamp span search with the correct formatting to read sequentially before and after that event, confirming it is part of an email processing flow. Use sub-agents if available to read the logs in the timestamps around each found entry in parallel for faster discovery.

### 2. Use Timestamp Spanning Search for Sequential Processing Logs

Once you identify the email processing timestamp, use timestamp spanning search to locate and read sequentially the processing logs from the moment that one email came in to when it was responded to or errored out:

```
fields @timestamp, @message
| filter toMillis(@timestamp) >= [start_millis] and toMillis(@timestamp) <= [end_millis]
| sort @timestamp asc
```

This will capture the complete email processing lifecycle from receipt to completion/error.

### 3. Line-by-Line Analysis

Based on line-by-line reading of the sequential logs, determine exactly what happened with the email by following the processing flow through each stage until processing stops.

## Common Email Processing Log Patterns

### Email Receipt

- `EMAIL_SAVED`
- `emailId: [number]`
- `subject: [subject_text]`
- `CoreAgentEmailSagaListener`

### Email Processing

- `processing email`
- `intent processing`
- `AI response generation`
- `email classification`

### Email Completion

- `email sent`
- `response generated`
- `processing complete`
- Error patterns: `ERROR`, `FAILED`, `exception`

## Efficiency Tips

### Do This First

1. **Always distinguish** between email processing logs and system/automated logs
2. **Use specific email processing patterns** rather than generic string searches
3. **Get millisecond timestamps** before doing time-range filtering
4. **Start with narrow time ranges** then expand

### Common Pitfalls to Avoid

1. **Don't assume** all occurrences of a string are email-related
2. **Don't use** message timestamp filtering - use proper @timestamp field
3. **Don't use** incorrect timestamp formats (see above)
4. **Don't search** too broadly initially - start narrow, then expand

### Search Strategy

1. Find when the email was received in the specific log group
2. Use timestamp spanning search for sequential processing logs
3. Perform line-by-line analysis to determine exactly what happened

## Example Complete Workflow

```bash
# Step 1: Find when the email was received in the specific log group
# Search for the subject or identifier within ClaroInfrastructureStack-PortalApiServiceTaskDefwebLogGroup603D6AA7-A8hmDo5Mgulz
fields @timestamp, @message | filter @message like /your_email_subject_or_identifier/ | sort @timestamp asc

# Confirm it's email processing by checking for email processing patterns
fields @timestamp, @message | filter @message like /your_email_subject_or_identifier/ and (@message like /EMAIL_SAVED/ or @message like /CoreAgentEmailSagaListener/ or @message like /emailId:/) | sort @timestamp asc

# Step 2: Get the millisecond timestamp for timestamp spanning
fields @timestamp, toMillis(@timestamp) as timestamp_millis, @message | filter @message like /your_email_subject_or_identifier/ and @message like /EMAIL_SAVED/ | sort @timestamp asc | limit 5

# Step 2: Use timestamp spanning search for sequential processing logs
# Read from email receipt to completion/error (e.g., ±2 minutes around the email)
fields @timestamp, @message | filter toMillis(@timestamp) >= [timestamp_millis - 120000] and toMillis(@timestamp) <= [timestamp_millis + 120000] | sort @timestamp asc

# Step 3: Line-by-line analysis
# Review the sequential logs to determine exactly what happened with the email processing
```

This 3-step approach ensures you locate the specific email processing logs within the correct log group and can trace the complete email journey from receipt to completion/error through line-by-line analysis.
