# Issue #7 Implementation Guide: Missing Status/Validation in Compliance Responses

## Executive Summary

This guide provides complete instructions for implementing a unified compliance error display system to fix Issue #7, where compliance responses lack structured status information and validation error blocks. The solution involves creating a reusable template fragment that can be included in all handlers that need to display compliance errors.

## Problem Analysis

**Issue Description**: Email subject `2604PARS161207381` receives a response mentioning "Port code missing" but lacks structured "Status:" sections and validation error blocks, forcing users to ask follow-up questions.

**Root Cause**: Compliance errors are currently displayed inconsistently across multiple templates with different formatting patterns, making it difficult to provide comprehensive status information.

## Current System Architecture

### 1. Compliance Error Data Flow

```
Validation → Formatting → Handler Context → Template Rendering
```

**Key Components:**
- **Validation**: `ComplianceValidationService` detects missing fields
- **Formatting**: `ShipmentServicesAdapter.formatMissingFieldsForTemplate()` (line 642)
- **Context**: `ShipmentContextService` builds template context
- **Display**: Multiple templates show errors inconsistently

### 2. Current Error Context Variables

**Primary Variables:**
- `complianceErrors`: Array of formatted error strings
- `missingFieldsAnalysis.formattedMissingFields`: Array like `["Port code **missing**", "Weight **missing**"]`
- `missingFieldsFormatted`: Joined string with newlines
- `complianceDetails.missingFieldsList`: List format for specific handlers
- `complianceDetails.hasMissingFields`: Boolean flag

**Field Formatting Source** (`/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts:642`):
```typescript
const fieldMapping = {
  etaPort: "ETA Port **missing**",
  etaDestination: "ETA Destination **missing**",
  portCode: "Port code **missing**",
  subLocation: "Sub-location **missing**",
  // ... more fields
};
```

### 3. Current Templates Displaying Compliance Errors

**Primary Templates:**
1. `/apps/portal-api/src/core-agent/templates/core-agent/fragments/status-message.njk`
   - Line 13: `{{ missingFieldsFormatted }}` (pending-commercial-invoice)
   - Line 18: `{{ missingFieldsFormatted }}` (pending-confirmation)

2. `/apps/portal-api/src/core-agent/templates/core-agent/fragments/details/status-line.njk`
   - Lines 2-3: `{{ complianceDetails.formattedMissingFields | join('<br />') }}`

3. `/apps/portal-api/src/core-agent/templates/core-agent/fragments/status-messages/pending-confirmation.njk`
   - Line 4: `{{ missingFieldsFormatted }}`

**Secondary Templates:**
- `document-requests/rush-processing-response.njk` (lines 18-19)
- `compliance-errors.njk` (basic error count)
- `customs-status.njk` (bullet point format)

### 4. Key Handlers Using Compliance Errors

**Primary Handlers:**
1. **`GetShipmentStatusHandler`** (`/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts`)
   - Uses `missingFieldsFormatted` in template context
   - Main handler for status inquiry responses

2. **`RequestRushProcessingHandler`** (`/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`)
   - Uses `complianceDetails.missingFieldsList` and `complianceDetails.hasMissingFields`

3. **`ProcessDocumentHandler`** (`/apps/portal-api/src/core-agent/handlers/process-document.handler.ts`)
   - Uses `smartTemplateContext` for document status display

## Implementation Requirements

### 1. Create Unified Compliance Error Fragment

**File to Create**: `/apps/portal-api/src/core-agent/templates/core-agent/fragments/compliance-error-block.njk`

**Requirements:**
- Display structured "Status:" section
- Show comprehensive validation error blocks
- Include detailed missing field information
- Maintain consistent formatting across all scenarios
- Support different error contexts (missing fields, validation errors, compliance issues)

**Template Structure:**
```njk
{# Unified compliance error display fragment
   Context requirements:
   - shipment.customsStatus: Current customs status
   - complianceDetails.formattedMissingFields: Array of formatted missing fields
   - complianceDetails.hasMissingFields: Boolean flag
   - formattedCustomsStatus: Human-readable status description
#}

{% if shipment.customsStatus == 'pending-confirmation' %}
<strong>Status:</strong> Pending Confirmation - Compliance issues need to be resolved<br />
<br />

{% if complianceDetails.hasMissingFields %}
<strong>Validation Errors:</strong><br />
{% for error in complianceDetails.formattedMissingFields %}
• {{ error | replace('**missing**', '<strong>Missing</strong>') | safe }}<br />
{% endfor %}
<br />
{% endif %}

{% elif shipment.customsStatus == 'pending-commercial-invoice' %}
<strong>Status:</strong> Pending Commercial Invoice - Missing required documents<br />
<br />
<strong>Missing Documents:</strong><br />
• CI & PL: <strong>Missing</strong><br />
{% if complianceDetails.hasMissingFields %}
{% for error in complianceDetails.formattedMissingFields %}
• {{ error | replace('**missing**', '<strong>Missing</strong>') | safe }}<br />
{% endfor %}
{% endif %}
<br />

{% else %}
{# For other statuses, show basic compliance info if available #}
{% if complianceDetails.hasMissingFields %}
<strong>Status:</strong> {{ formattedCustomsStatus }}<br />
<br />
<strong>Additional Requirements:</strong><br />
{% for error in complianceDetails.formattedMissingFields %}
• {{ error | replace('**missing**', '<strong>Missing</strong>') | safe }}<br />
{% endfor %}
<br />
{% endif %}
{% endif %}
```

### 2. Update Handler Context Standardization

**File to Modify**: `/apps/portal-api/src/core-agent/services/shipment-response.service.ts`

**Requirements:**
- Ensure all handlers provide consistent `complianceDetails` context
- Standardize `formattedMissingFields` array format
- Include `hasMissingFields` boolean flag
- Provide `formattedCustomsStatus` for all handlers

**Context Structure:**
```typescript
interface ComplianceDetails {
  formattedMissingFields: string[];
  hasMissingFields: boolean;
  missingFieldsList: string[];
}

interface FragmentContext {
  shipment: {
    customsStatus: string;
    // ... other shipment fields
  };
  complianceDetails: ComplianceDetails;
  formattedCustomsStatus: string;
}
```

### 3. Update Templates to Use Unified Fragment

**Templates to Modify:**

1. **`status-message.njk`** - Replace inline error display with fragment include:
   ```njk
   {% if shipment.customsStatus == 'pending-commercial-invoice' %}
   <p>Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.</p>
   
   {% include "core-agent/fragments/compliance-error-block.njk" %}
   
   {% elif shipment.customsStatus == 'pending-confirmation' %}
   There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.
   
   {% include "core-agent/fragments/compliance-error-block.njk" %}
   
   {% endif %}
   ```

2. **`status-line.njk`** - Replace direct error display:
   ```njk
   {% include "core-agent/fragments/compliance-error-block.njk" %}
   Status: <strong>{{ formattedCustomsStatus or shipment.customsStatus }}</strong>
   ```

3. **`pending-confirmation.njk`** - Replace inline error display:
   ```njk
   {% include "core-agent/fragments/compliance-error-block.njk" %}
   ```

### 4. Update Handlers to Use Unified Context

**Handlers to Modify:**

1. **`GetShipmentStatusHandler`** - Ensure consistent context:
   ```typescript
   // In generateStatusResponse method
   const fragmentContext = {
     shipment: context.shipment,
     complianceDetails: {
       formattedMissingFields: context.missingFieldsAnalysis.formattedMissingFields || [],
       hasMissingFields: context.missingFieldsAnalysis.formattedMissingFields?.length > 0,
       missingFieldsList: context.missingFieldsAnalysis.formattedMissingFields || []
     },
     formattedCustomsStatus: context.formattedCustomsStatus
   };
   ```

2. **`RequestRushProcessingHandler`** - Standardize context format:
   ```typescript
   // Ensure complianceDetails matches expected structure
   const fragmentContext = {
     shipment: context.shipment,
     complianceDetails: {
       formattedMissingFields: context.missingFieldsAnalysis.formattedMissingFields || [],
       hasMissingFields: context.missingFieldsAnalysis.formattedMissingFields?.length > 0,
       missingFieldsList: context.missingFieldsAnalysis.formattedMissingFields || []
     },
     formattedCustomsStatus: context.formattedCustomsStatus
   };
   ```

## Testing Requirements

### 1. Test Scenarios

**Test Case 1: Port Code Missing (Issue #7 Example)**
- **Email Subject**: `2604PARS161207381`
- **Expected Response**: 
  ```
  Status: Pending Confirmation - Compliance issues need to be resolved
  
  Validation Errors:
  • Port code Missing
  
  [Additional status information]
  ```

**Test Case 2: Multiple Missing Fields**
- **Scenario**: Shipment with missing port code, weight, and sub-location
- **Expected Response**:
  ```
  Status: Pending Confirmation - Compliance issues need to be resolved
  
  Validation Errors:
  • Port code Missing
  • Weight Missing
  • Sub-location Missing
  ```

**Test Case 3: Pending Commercial Invoice**
- **Scenario**: Shipment requiring CI & PL documents
- **Expected Response**:
  ```
  Status: Pending Commercial Invoice - Missing required documents
  
  Missing Documents:
  • CI & PL: Missing
  ```

### 2. Validation Steps

1. **Template Rendering**: Verify fragment renders correctly with different context combinations
2. **Handler Integration**: Ensure all handlers provide consistent context structure
3. **Backwards Compatibility**: Confirm existing functionality remains intact
4. **Error Handling**: Test fragment behavior with missing or invalid context data

### 3. Manual Testing

**Test Commands:**
```bash
# Run tests in portal-api
cd apps/portal-api && rushx jest

# Check TypeScript compilation
cd apps/portal-api && rushx lint
```

## Implementation Checklist

- [ ] Create `compliance-error-block.njk` fragment template
- [ ] Update `status-message.njk` to use fragment include
- [ ] Update `status-line.njk` to use fragment include
- [ ] Update `pending-confirmation.njk` to use fragment include
- [ ] Modify `GetShipmentStatusHandler` for consistent context
- [ ] Modify `RequestRushProcessingHandler` for consistent context
- [ ] Update `shipment-response.service.ts` for standardized context
- [ ] Test all compliance error scenarios
- [ ] Verify backwards compatibility
- [ ] Update any additional templates found during implementation

## Expected Outcomes

**Before Fix:**
```
System replies that there are compliance issues but only mentions Port code missing; it omits a structured "Status:" line and any validation-error block
```

**After Fix:**
```
Status: Pending Confirmation - Compliance issues need to be resolved

Validation Errors:
• Port code Missing

[Additional comprehensive status information]
```

## Technical Notes

1. **Fragment Include Pattern**: Use `{% include "core-agent/fragments/compliance-error-block.njk" %}`
2. **Context Requirement**: All handlers must provide `complianceDetails` with standardized structure
3. **HTML Formatting**: Use `<strong>` tags for emphasis, `<br />` for line breaks
4. **Error Handling**: Fragment should gracefully handle missing context variables
5. **Backwards Compatibility**: Maintain existing context variables during transition

## Related Files Reference

**Key Files for Implementation:**
- `/apps/portal-api/src/core-agent/templates/core-agent/fragments/status-message.njk`
- `/apps/portal-api/src/core-agent/templates/core-agent/fragments/details/status-line.njk`
- `/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts`
- `/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`
- `/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts` (line 642)
- `/apps/portal-api/src/core-agent/services/shipment-response.service.ts`

**Context Variables Sources:**
- `complianceErrors`: Generated by `ComplianceValidationService`
- `missingFieldsAnalysis`: Built by `ShipmentContextService`
- `formattedMissingFields`: Created by `ShipmentServicesAdapter.formatMissingFieldsForTemplate()`

This implementation will resolve Issue #7 by providing structured, comprehensive compliance error information that prevents users from needing to ask follow-up questions.